#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时段区间阻抗系统实现 - 自适应版本
基于历史交通数据的时段区间阻抗划分方法，支持动态调整时段数量
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class TrafficDataPreprocessor:
    """交通数据预处理器"""
    
    def __init__(self):
        self.data = None
        self.cleaned_data = None
        
    def load_data(self, file_paths):
        """加载多个CSV文件的交通数据"""
        all_data = []
        
        for file_path in file_paths:
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                all_data.append(df)
                print(f"成功加载文件: {file_path}, 记录数: {len(df)}")
            except Exception as e:
                print(f"加载文件失败 {file_path}: {e}")
                
        if all_data:
            self.data = pd.concat(all_data, ignore_index=True)
            print(f"总计加载记录数: {len(self.data)}")
        else:
            raise ValueError("没有成功加载任何数据文件")
            
    def clean_data(self):
        """数据清洗和预处理"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        self.cleaned_data = self.data.copy()
        
        # 1. 处理时间字段
        self.cleaned_data['采集时间'] = pd.to_datetime(self.cleaned_data['采集时间'])
        
        # 2. 提取时间特征
        self.cleaned_data['小时'] = self.cleaned_data['采集时间'].dt.hour
        self.cleaned_data['分钟'] = self.cleaned_data['采集时间'].dt.minute
        self.cleaned_data['星期'] = self.cleaned_data['采集时间'].dt.weekday
        self.cleaned_data['时间分钟'] = self.cleaned_data['小时'] * 60 + self.cleaned_data['分钟']
        
        # 3. 计算速度 (米/秒)
        self.cleaned_data['速度'] = self.cleaned_data['距离(米)'] / self.cleaned_data['通行时间(秒)']
        
        # 4. 异常值检测和处理 (使用IQR方法)
        for road_id in self.cleaned_data['路段ID'].unique():
            road_mask = self.cleaned_data['路段ID'] == road_id
            road_data = self.cleaned_data[road_mask]['通行时间(秒)']
            
            Q1 = road_data.quantile(0.25)
            Q3 = road_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 标记异常值
            outlier_mask = road_mask & (
                (self.cleaned_data['通行时间(秒)'] < lower_bound) | 
                (self.cleaned_data['通行时间(秒)'] > upper_bound)
            )
            
            outlier_count = outlier_mask.sum()
            if outlier_count > 0:
                print(f"路段 {road_id}: 检测到 {outlier_count} 个异常值")
                
        # 移除异常值
        valid_mask = True
        for road_id in self.cleaned_data['路段ID'].unique():
            road_mask = self.cleaned_data['路段ID'] == road_id
            road_data = self.cleaned_data[road_mask]['通行时间(秒)']
            
            Q1 = road_data.quantile(0.25)
            Q3 = road_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            road_valid_mask = ~road_mask | (
                (self.cleaned_data['通行时间(秒)'] >= lower_bound) & 
                (self.cleaned_data['通行时间(秒)'] <= upper_bound)
            )
            valid_mask = valid_mask & road_valid_mask
            
        self.cleaned_data = self.cleaned_data[valid_mask].reset_index(drop=True)
        print(f"清洗后数据记录数: {len(self.cleaned_data)}")
        
    def aggregate_by_time_intervals(self, interval_minutes=10):
        """按时间间隔聚合数据 - 使用更小的时间间隔以获得更多时段"""
        if self.cleaned_data is None:
            raise ValueError("请先进行数据清洗")
            
        # 计算时间间隔索引
        self.cleaned_data['时间间隔'] = self.cleaned_data['时间分钟'] // interval_minutes
        
        # 按路段和时间间隔聚合
        aggregated = self.cleaned_data.groupby(['路段ID', '时间间隔']).agg({
            '通行时间(秒)': ['mean', 'std', 'min', 'max', 'count'],
            '速度': ['mean', 'std'],
            '距离(米)': 'first'
        }).reset_index()
        
        # 扁平化列名
        aggregated.columns = [
            '路段ID', '时间间隔', 
            '通行时间_均值', '通行时间_标准差', '通行时间_最小值', '通行时间_最大值', '样本数量',
            '速度_均值', '速度_标准差', '距离'
        ]
        
        # 降低样本数量要求，以保留更多时间间隔
        aggregated = aggregated[aggregated['样本数量'] >= 2].reset_index(drop=True)
        
        return aggregated

class AdaptiveTimeSegmentPartitioner:
    """自适应时段划分器 - 动态调整时段数量"""
    
    def __init__(self, min_segments=5, max_segments=8, min_samples_per_segment=3):
        self.min_segments = min_segments
        self.max_segments = max_segments
        self.min_samples_per_segment = min_samples_per_segment
        self.segments = {}
        
    def determine_optimal_segments(self, road_data):
        """根据数据特征确定最优时段数量"""
        time_intervals = sorted(road_data['时间间隔'].unique())
        n_intervals = len(time_intervals)
        
        # 如果时间间隔数量太少，直接返回最小时段数
        if n_intervals <= self.min_segments:
            return min(n_intervals, self.max_segments)
            
        # 计算数据特征
        means = []
        stds = []
        sample_counts = []
        
        for interval in time_intervals:
            interval_data = road_data[road_data['时间间隔'] == interval]
            if len(interval_data) > 0:
                means.append(interval_data['通行时间_均值'].iloc[0])
                stds.append(interval_data['通行时间_标准差'].iloc[0] if not pd.isna(interval_data['通行时间_标准差'].iloc[0]) else 0)
                sample_counts.append(interval_data['样本数量'].iloc[0])
            else:
                means.append(0)
                stds.append(0)
                sample_counts.append(0)
                
        # 特征1: 数据变化程度 (变异系数)
        cv = np.std(means) / np.mean(means) if np.mean(means) > 0 else 0
        
        # 特征2: 总样本数量
        total_samples = sum(sample_counts)
        
        # 特征3: 时间间隔数量
        interval_count = len(time_intervals)
        
        # 动态决策逻辑
        optimal_segments = self.min_segments
        
        # 基于变异系数调整
        if cv > 0.15:  # 高变异性
            optimal_segments += 2
        elif cv > 0.08:  # 中等变异性
            optimal_segments += 1
            
        # 基于样本数量调整
        if total_samples > 200:  # 充足样本
            optimal_segments += 1
        elif total_samples < 50:  # 样本不足
            optimal_segments -= 1
            
        # 基于时间间隔数量调整
        if interval_count >= 10:  # 时间间隔充足
            optimal_segments += 1
        elif interval_count <= 6:  # 时间间隔不足
            optimal_segments -= 1
            
        # 确保在合理范围内
        optimal_segments = max(self.min_segments, min(self.max_segments, optimal_segments))
        optimal_segments = min(optimal_segments, interval_count)  # 不能超过可用间隔数
        
        return optimal_segments
        
    def partition_with_adaptive_segments(self, road_data):
        """使用自适应时段数量进行划分"""
        optimal_segments = self.determine_optimal_segments(road_data)
        
        # 尝试多种划分方法，选择最佳结果
        methods = [
            ('change_rate', self._partition_by_change_rate),
            ('statistical', self._partition_by_statistical_test),
            ('adaptive_threshold', self._partition_by_adaptive_threshold),
            ('uniform', self._uniform_partition)
        ]
        
        best_segments = None
        best_score = -1
        best_method = None
        
        for method_name, method_func in methods:
            try:
                segments = method_func(road_data, optimal_segments)
                score = self._evaluate_segmentation_quality(road_data, segments)
                
                if score > best_score and self.min_segments <= len(segments) <= self.max_segments:
                    best_segments = segments
                    best_score = score
                    best_method = method_name
                    
            except Exception as e:
                continue
                
        # 如果没有找到合适的划分，使用均匀划分作为兜底
        if best_segments is None:
            best_segments = self._uniform_partition(road_data, optimal_segments)
            best_method = 'uniform_fallback'
            
        return best_segments, optimal_segments, best_method, best_score

    def _evaluate_segmentation_quality(self, road_data, segments):
        """评估时段划分质量"""
        if not segments or len(segments) == 0:
            return 0

        # 计算时段间差异性和时段内一致性
        segment_means = []
        segment_vars = []

        for segment_intervals in segments:
            if not segment_intervals:
                continue

            segment_data = road_data[road_data['时间间隔'].isin(segment_intervals)]
            if len(segment_data) == 0:
                continue

            # 计算时段内的统计量
            travel_times = segment_data['通行时间_均值'].values
            if len(travel_times) > 0:
                segment_means.append(np.mean(travel_times))
                segment_vars.append(np.var(travel_times) if len(travel_times) > 1 else 0)

        if len(segment_means) < 2:
            return 0

        # 时段间差异性 (越大越好)
        inter_segment_variance = np.var(segment_means)

        # 时段内一致性 (越小越好)
        avg_intra_segment_variance = np.mean(segment_vars)

        # F统计量作为质量评分
        if avg_intra_segment_variance > 0:
            f_score = inter_segment_variance / avg_intra_segment_variance
        else:
            f_score = inter_segment_variance

        # 考虑时段数量的合理性
        segment_count_penalty = 1.0
        if len(segments) < self.min_segments or len(segments) > self.max_segments:
            segment_count_penalty = 0.5

        return f_score * segment_count_penalty

    def _partition_by_change_rate(self, road_data, target_segments):
        """基于变化率的时段划分"""
        time_intervals = sorted(road_data['时间间隔'].unique())

        if len(time_intervals) < 2:
            return [time_intervals]

        # 计算相邻时间间隔的通行时间变化率
        changes = []
        means = []

        for interval in time_intervals:
            interval_data = road_data[road_data['时间间隔'] == interval]
            if len(interval_data) > 0:
                means.append(interval_data['通行时间_均值'].iloc[0])
            else:
                means.append(0)

        # 计算变化率
        for i in range(1, len(means)):
            if means[i-1] != 0:
                change_rate = abs((means[i] - means[i-1]) / means[i-1])
            else:
                change_rate = 0
            changes.append((i, change_rate))

        # 按变化率排序，选择变化最大的点作为分割点
        changes.sort(key=lambda x: x[1], reverse=True)

        # 选择前(target_segments-1)个变化点
        split_points = sorted([change[0] for change in changes[:target_segments-1]])

        # 构建时段
        segments = []
        start_idx = 0

        for split_point in split_points:
            if split_point > start_idx:
                segments.append(time_intervals[start_idx:split_point])
                start_idx = split_point

        # 添加最后一个时段
        if start_idx < len(time_intervals):
            segments.append(time_intervals[start_idx:])

        return [seg for seg in segments if len(seg) > 0]

    def _partition_by_statistical_test(self, road_data, target_segments, significance_level=0.15):
        """基于统计检验的时段划分方法"""
        time_intervals = sorted(road_data['时间间隔'].unique())

        if len(time_intervals) < 2:
            return [time_intervals]

        # 如果时间间隔数量少于目标时段数，直接返回每个间隔作为一个时段
        if len(time_intervals) <= target_segments:
            return [[interval] for interval in time_intervals]

        segments = []
        current_segment = [time_intervals[0]]

        for i in range(1, len(time_intervals)):
            current_interval = time_intervals[i]

            # 获取当前时段和前一时段的数据
            current_data = road_data[road_data['时间间隔'] == current_interval]['通行时间_均值'].values
            previous_data = road_data[road_data['时间间隔'].isin(current_segment)]['通行时间_均值'].values

            # 进行t检验 (如果样本数量足够)
            should_split = False
            if len(current_data) > 0 and len(previous_data) > 1:
                try:
                    t_stat, p_value = stats.ttest_ind(current_data, previous_data)
                    if p_value < significance_level:
                        should_split = True
                except:
                    pass

            # 如果已经达到目标时段数，停止分割
            if should_split and len(segments) < target_segments - 1:
                segments.append(current_segment)
                current_segment = [current_interval]
            else:
                current_segment.append(current_interval)

        segments.append(current_segment)
        return segments

    def _partition_by_adaptive_threshold(self, road_data, target_segments):
        """基于自适应阈值的时段划分"""
        time_intervals = sorted(road_data['时间间隔'].unique())

        if len(time_intervals) < 2:
            return [time_intervals]

        # 获取所有时间间隔的通行时间均值
        means = []
        for interval in time_intervals:
            interval_data = road_data[road_data['时间间隔'] == interval]
            if len(interval_data) > 0:
                means.append(interval_data['通行时间_均值'].iloc[0])
            else:
                means.append(0)

        # 计算全局标准差
        global_std = np.std(means) if len(means) > 1 else 0

        # 自适应阈值：从严格到宽松逐步尝试
        thresholds = [0.15 * global_std, 0.1 * global_std, 0.05 * global_std, 0.02 * global_std]

        for threshold in thresholds:
            segments = []
            current_segment = [time_intervals[0]]
            current_mean = means[0]

            for i in range(1, len(time_intervals)):
                # 如果当前均值与时段均值差异超过阈值，开始新时段
                if abs(means[i] - current_mean) > threshold and len(segments) < target_segments - 1:
                    segments.append(current_segment)
                    current_segment = [time_intervals[i]]
                    current_mean = means[i]
                else:
                    current_segment.append(time_intervals[i])
                    # 更新时段均值
                    segment_means = [means[time_intervals.index(t)] for t in current_segment]
                    current_mean = np.mean(segment_means)

            segments.append(current_segment)

            # 如果达到合理的时段数，返回结果
            if self.min_segments <= len(segments) <= self.max_segments:
                return segments

        # 如果所有阈值都无法达到目标，使用均匀划分
        return self._uniform_partition(time_intervals, target_segments)

    def _uniform_partition(self, time_intervals, target_segments):
        """均匀划分时间间隔"""
        if len(time_intervals) <= target_segments:
            return [[interval] for interval in time_intervals]

        segment_size = len(time_intervals) // target_segments
        segments = []

        for i in range(0, len(time_intervals), segment_size):
            segment = time_intervals[i:i + segment_size]
            if segment:
                segments.append(segment)

        # 如果有剩余的间隔，合并到最后一个时段
        if len(segments) > target_segments:
            last_segment = segments.pop()
            segments[-1].extend(last_segment)

        return segments

class ImpedanceIntervalCalculator:
    """阻抗区间计算器"""

    def __init__(self, confidence_level=0.95):
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level

    def calculate_interval(self, segment_data, original_data):
        """计算时段阻抗区间"""
        if len(segment_data) == 0:
            return None

        # 获取原始数据用于计算区间
        segment_intervals = segment_data['时间间隔'].tolist()
        raw_travel_times = []

        for interval in segment_intervals:
            interval_data = original_data[original_data['时间间隔'] == interval]['通行时间(秒)']
            raw_travel_times.extend(interval_data.tolist())

        if len(raw_travel_times) < 2:
            return None

        travel_times = np.array(raw_travel_times)
        n = len(travel_times)

        # 基本统计量
        mean = np.mean(travel_times)
        std = np.std(travel_times, ddof=1)

        # 方法1：基于正态分布的置信区间
        t_critical = stats.t.ppf(1 - self.alpha/2, df=n-1)
        margin_error = t_critical * std / np.sqrt(n)

        normal_interval = {
            'lower': max(0, mean - margin_error),
            'upper': mean + margin_error,
            'method': 'normal_confidence'
        }

        # 方法2：基于分位数的区间
        percentile_interval = {
            'lower': np.percentile(travel_times, (self.alpha/2) * 100),
            'upper': np.percentile(travel_times, (1 - self.alpha/2) * 100),
            'method': 'percentile'
        }

        # 方法3：基于经验分布的区间
        empirical_interval = {
            'lower': np.min(travel_times),
            'upper': np.max(travel_times),
            'method': 'empirical'
        }

        return {
            'normal': normal_interval,
            'percentile': percentile_interval,
            'empirical': empirical_interval,
            'statistics': {
                'mean': mean,
                'std': std,
                'n': n,
                'cv': std / mean if mean > 0 else float('inf'),
                'median': np.median(travel_times)
            }
        }

class AdaptiveTimeSegmentImpedanceSystem:
    """自适应时段区间阻抗系统主类"""

    def __init__(self, confidence_level=0.95, min_segments=5, max_segments=8):
        self.preprocessor = TrafficDataPreprocessor()
        self.partitioner = AdaptiveTimeSegmentPartitioner(min_segments, max_segments)
        self.calculator = ImpedanceIntervalCalculator(confidence_level)
        self.road_segments = {}
        self.original_data = None
        self.segment_statistics = {}  # 存储每个路段的划分统计信息

    def build_system(self, data_files, partition_method='adaptive'):
        """构建完整的自适应时段区间阻抗系统"""
        print("=== 开始构建自适应时段区间阻抗系统 ===")

        # 1. 数据预处理
        print("\n1. 数据预处理...")
        self.preprocessor.load_data(data_files)
        self.preprocessor.clean_data()
        aggregated_data = self.preprocessor.aggregate_by_time_intervals(interval_minutes=10)

        # 保存原始清洗后的数据和时间间隔大小
        self.original_data = self.preprocessor.cleaned_data
        self.interval_minutes = 10  # 保存时间间隔大小

        print(f"聚合后数据: {len(aggregated_data)} 条记录")

        # 2. 为每条道路进行自适应时段划分
        print("\n2. 自适应时段划分...")
        road_ids = aggregated_data['路段ID'].unique()

        for i, road_id in enumerate(road_ids):
            print(f"处理路段 {road_id} ({i+1}/{len(road_ids)})")

            road_data = aggregated_data[aggregated_data['路段ID'] == road_id].copy()

            if len(road_data) < 2:
                print(f"  路段 {road_id} 数据不足，跳过")
                continue

            # 自适应时段划分
            segments, optimal_segments, method_used, quality_score = self.partitioner.partition_with_adaptive_segments(road_data)

            print(f"  目标时段数: {optimal_segments}, 实际划分: {len(segments)}个时段, 方法: {method_used}, 质量评分: {quality_score:.2f}")

            # 保存划分统计信息
            self.segment_statistics[road_id] = {
                'target_segments': optimal_segments,
                'actual_segments': len(segments),
                'method_used': method_used,
                'quality_score': quality_score,
                'time_intervals_count': len(road_data)
            }

            # 计算每个时段的阻抗区间
            road_intervals = {}
            for j, segment_intervals in enumerate(segments):
                segment_data = road_data[road_data['时间间隔'].isin(segment_intervals)]

                if len(segment_data) == 0:
                    continue

                # 获取原始数据
                original_segment_data = self.original_data[
                    (self.original_data['路段ID'] == road_id) &
                    (self.original_data['时间间隔'].isin(segment_intervals))
                ]

                interval_result = self.calculator.calculate_interval(segment_data, original_segment_data)

                if interval_result:
                    start_time = min(segment_intervals) * self.interval_minutes  # 使用动态时间间隔
                    end_time = (max(segment_intervals) + 1) * self.interval_minutes

                    road_intervals[f'segment_{j}'] = {
                        'time_intervals': segment_intervals,
                        'impedance_intervals': interval_result,
                        'start_time': start_time,
                        'end_time': end_time,
                        'start_time_str': f"{start_time//60:02d}:{start_time%60:02d}",
                        'end_time_str': f"{end_time//60:02d}:{end_time%60:02d}"
                    }

            self.road_segments[road_id] = road_intervals

        print(f"\n成功处理 {len(self.road_segments)} 个路段")

    def get_impedance_interval(self, road_id, current_time_minutes, method='percentile'):
        """获取指定道路在指定时间的阻抗区间"""
        if road_id not in self.road_segments:
            return None

        road_data = self.road_segments[road_id]

        for segment_info in road_data.values():
            if segment_info['start_time'] <= current_time_minutes < segment_info['end_time']:
                return segment_info['impedance_intervals'][method]

        return None

    def get_road_summary(self, road_id):
        """获取路段的时段划分摘要"""
        if road_id not in self.road_segments:
            return None

        road_data = self.road_segments[road_id]
        summary = {
            'road_id': road_id,
            'num_segments': len(road_data),
            'segments': []
        }

        for segment_info in road_data.values():
            segment_summary = {
                'time_range': f"{segment_info['start_time_str']} - {segment_info['end_time_str']}",
                'mean_travel_time': segment_info['impedance_intervals']['statistics']['mean'],
                'std_travel_time': segment_info['impedance_intervals']['statistics']['std'],
                'percentile_interval': segment_info['impedance_intervals']['percentile']
            }
            summary['segments'].append(segment_summary)

        return summary

    def validate_system(self):
        """验证系统有效性"""
        print("\n=== 系统验证 ===")
        validation_results = {}

        for road_id, road_data in self.road_segments.items():
            if len(road_data) < 2:
                continue

            # 检查时段间差异性
            segment_means = []
            segment_stds = []

            for segment_info in road_data.values():
                stats = segment_info['impedance_intervals']['statistics']
                segment_means.append(stats['mean'])
                segment_stds.append(stats['std'])

            # 计算时段间方差
            inter_segment_variance = np.var(segment_means) if len(segment_means) > 1 else 0

            # 计算平均时段内方差
            avg_intra_segment_variance = np.mean([std**2 for std in segment_stds])

            # F统计量
            f_statistic = (inter_segment_variance / avg_intra_segment_variance
                          if avg_intra_segment_variance > 0 else float('inf'))

            # 变异系数
            cv_values = [segment_info['impedance_intervals']['statistics']['cv']
                        for segment_info in road_data.values()]
            avg_cv = np.mean(cv_values)

            validation_results[road_id] = {
                'f_statistic': f_statistic,
                'inter_segment_variance': inter_segment_variance,
                'avg_intra_segment_variance': avg_intra_segment_variance,
                'num_segments': len(road_data),
                'avg_cv': avg_cv,
                'segment_means': segment_means
            }

        return validation_results

    def generate_adaptive_report(self, output_file='时段划分程序/系统2/系统2运行结果.txt'):
        """生成自适应分析报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("自适应时段区间阻抗系统分析报告\n")
            f.write("=" * 60 + "\n\n")

            # 系统概览
            f.write("1. 系统概览\n")
            f.write("-" * 30 + "\n")
            f.write(f"处理路段数量: {len(self.road_segments)}\n")
            f.write(f"原始数据记录数: {len(self.original_data)}\n")
            f.write(f"数据时间范围: {self.original_data['采集时间'].min()} 到 {self.original_data['采集时间'].max()}\n\n")

            # 自适应划分统计
            f.write("2. 自适应划分统计\n")
            f.write("-" * 30 + "\n")

            segment_counts = [stats['actual_segments'] for stats in self.segment_statistics.values()]
            f.write(f"时段数量分布:\n")
            for i in range(5, 9):
                count = sum(1 for x in segment_counts if x == i)
                percentage = (count / len(segment_counts)) * 100 if segment_counts else 0
                f.write(f"  {i}个时段: {count}个路段 ({percentage:.1f}%)\n")

            f.write(f"平均时段数量: {np.mean(segment_counts):.1f}\n")
            f.write(f"时段数量标准差: {np.std(segment_counts):.1f}\n\n")

            # 方法使用统计
            f.write("3. 划分方法统计\n")
            f.write("-" * 30 + "\n")
            methods = [stats['method_used'] for stats in self.segment_statistics.values()]
            method_counts = {}
            for method in methods:
                method_counts[method] = method_counts.get(method, 0) + 1

            for method, count in method_counts.items():
                percentage = (count / len(methods)) * 100 if methods else 0
                f.write(f"  {method}: {count}个路段 ({percentage:.1f}%)\n")
            f.write("\n")

            # 验证结果
            validation_results = self.validate_system()
            f.write("4. 系统验证结果\n")
            f.write("-" * 30 + "\n")

            valid_roads = 0
            total_segments = 0
            quality_scores = [stats['quality_score'] for stats in self.segment_statistics.values()]

            for road_id, result in validation_results.items():
                if result['f_statistic'] > 1.0:  # F统计量大于1表示时段间差异明显
                    valid_roads += 1
                total_segments += result['num_segments']

            f.write(f"有效路段数量: {valid_roads}/{len(validation_results)}\n")
            f.write(f"平均时段数量: {total_segments/len(validation_results):.1f}\n")
            f.write(f"平均F统计量: {np.mean([r['f_statistic'] for r in validation_results.values() if not np.isinf(r['f_statistic'])]):.2f}\n")
            f.write(f"平均质量评分: {np.mean(quality_scores):.2f}\n\n")

            # 详细路段信息
            f.write("5. 路段详细信息\n")
            f.write("-" * 30 + "\n")

            for road_id in sorted(self.road_segments.keys()):
                summary = self.get_road_summary(road_id)
                stats = self.segment_statistics.get(road_id, {})

                if summary:
                    f.write(f"\n路段 {road_id}:\n")
                    f.write(f"  时段数量: {summary['num_segments']} (目标: {stats.get('target_segments', 'N/A')})\n")
                    f.write(f"  划分方法: {stats.get('method_used', 'N/A')}\n")
                    f.write(f"  质量评分: {stats.get('quality_score', 0):.2f}\n")

                    for segment in summary['segments']:
                        f.write(f"  {segment['time_range']}: ")
                        f.write(f"均值={segment['mean_travel_time']:.1f}秒, ")
                        f.write(f"区间=[{segment['percentile_interval']['lower']:.1f}, {segment['percentile_interval']['upper']:.1f}]秒\n")

        print(f"自适应分析报告已生成: {output_file}")

    def compare_with_traditional_method(self, road_id):
        """与传统区间阻抗方法对比"""
        if road_id not in self.road_segments:
            print(f"路段 {road_id} 不存在")
            return None

        # 获取原始数据
        road_data = self.original_data[self.original_data['路段ID'] == road_id]['通行时间(秒)']

        if len(road_data) == 0:
            return None

        # 传统方法：整体区间
        traditional_interval = {
            'lower': road_data.quantile(0.025),
            'upper': road_data.quantile(0.975),
            'width': road_data.quantile(0.975) - road_data.quantile(0.025)
        }

        # 自适应时段区间方法：各时段区间
        segment_intervals = []
        for segment_info in self.road_segments[road_id].values():
            interval_data = segment_info['impedance_intervals']['percentile']
            segment_intervals.append({
                'lower': interval_data['lower'],
                'upper': interval_data['upper'],
                'width': interval_data['upper'] - interval_data['lower'],
                'time_range': f"{segment_info['start_time_str']}-{segment_info['end_time_str']}"
            })

        # 计算改进指标
        avg_segment_width = np.mean([seg['width'] for seg in segment_intervals])
        width_reduction = (traditional_interval['width'] - avg_segment_width) / traditional_interval['width'] * 100

        comparison = {
            'road_id': road_id,
            'traditional': traditional_interval,
            'segments': segment_intervals,
            'improvement': {
                'width_reduction_percent': width_reduction,
                'avg_segment_width': avg_segment_width,
                'num_segments': len(segment_intervals)
            }
        }

        return comparison

def main():
    """主函数 - 演示自适应时段区间阻抗系统的使用"""
    print("自适应时段区间阻抗系统演示")
    print("=" * 50)

    # 数据文件路径
    data_files = [
        '采集数据/早高峰/augment7.22早高峰.csv',
        '采集数据/早高峰/augment7.23早高峰.csv',
        '采集数据/早高峰/augment7.24早高峰.csv',
        '采集数据/早高峰/augment7.25早高峰.csv',
        '采集数据/早高峰/augment7.26早高峰.csv',
        '采集数据/早高峰/augment7.27早高峰.csv',
        '采集数据/早高峰/augment7.28早高峰.csv',
        '采集数据/早高峰/augment7.29早高峰.csv',
        '采集数据/早高峰/augment7.30早高峰.csv',
        '采集数据/早高峰/augment7.31早高峰.csv',
        '采集数据/早高峰/augment8.1早高峰.csv',
        '采集数据/早高峰/augment8.2早高峰.csv',
        '采集数据/早高峰/augment8.3早高峰.csv',
        '采集数据/早高峰/augment8.4早高峰.csv'
    ]

    # 检查文件是否存在
    import os
    existing_files = [f for f in data_files if os.path.exists(f)]

    if not existing_files:
        print("错误：找不到数据文件")
        print("请确保以下文件存在：")
        for f in data_files:
            print(f"  - {f}")
        return

    print(f"找到 {len(existing_files)} 个数据文件")

    # 创建自适应系统实例
    system = AdaptiveTimeSegmentImpedanceSystem(confidence_level=0.95, min_segments=5, max_segments=8)

    try:
        # 构建系统
        system.build_system(existing_files, partition_method='adaptive')

        # 生成验证报告
        validation_results = system.validate_system()

        # 分析时段数量分布
        print(f"\n=== 自适应时段划分结果分析 ===")
        segment_counts = [stats['actual_segments'] for stats in system.segment_statistics.values()]

        print("时段数量分布:")
        for i in range(5, 9):
            count = sum(1 for x in segment_counts if x == i)
            percentage = (count / len(segment_counts)) * 100 if segment_counts else 0
            print(f"  {i}个时段: {count}个路段 ({percentage:.1f}%)")

        print(f"平均时段数量: {np.mean(segment_counts):.1f}")
        print(f"时段数量标准差: {np.std(segment_counts):.1f}")

        # 分析划分方法使用情况
        print(f"\n=== 划分方法使用统计 ===")
        methods = [stats['method_used'] for stats in system.segment_statistics.values()]
        method_counts = {}
        for method in methods:
            method_counts[method] = method_counts.get(method, 0) + 1

        for method, count in method_counts.items():
            percentage = (count / len(methods)) * 100 if methods else 0
            print(f"  {method}: {count}个路段 ({percentage:.1f}%)")

        # 选择几个代表性路段进行详细分析
        sample_roads = list(system.road_segments.keys())[:5]  # 取前5个路段

        print(f"\n=== 详细分析示例路段 ===")
        for road_id in sample_roads:
            print(f"\n路段 {road_id}:")
            stats = system.segment_statistics.get(road_id, {})
            print(f"  目标时段数: {stats.get('target_segments', 'N/A')}")
            print(f"  实际时段数: {stats.get('actual_segments', 'N/A')}")
            print(f"  划分方法: {stats.get('method_used', 'N/A')}")
            print(f"  质量评分: {stats.get('quality_score', 0):.2f}")

            # 获取路段摘要
            summary = system.get_road_summary(road_id)
            if summary:
                for segment in summary['segments']:
                    print(f"    {segment['time_range']}: 均值={segment['mean_travel_time']:.1f}秒, "
                          f"区间=[{segment['percentile_interval']['lower']:.1f}, "
                          f"{segment['percentile_interval']['upper']:.1f}]秒")

            # 与传统方法对比
            comparison = system.compare_with_traditional_method(road_id)
            if comparison:
                print(f"  传统方法区间宽度: {comparison['traditional']['width']:.1f}秒")
                print(f"  自适应方法平均宽度: {comparison['improvement']['avg_segment_width']:.1f}秒")
                print(f"  区间宽度减少: {comparison['improvement']['width_reduction_percent']:.1f}%")

        # 生成自适应分析报告
        system.generate_adaptive_report()

        # 演示实时查询功能
        print(f"\n=== 实时查询演示 ===")
        test_times = [7*60+30, 8*60, 8*60+30]  # 7:30, 8:00, 8:30

        for road_id in sample_roads[:3]:
            print(f"\n路段 {road_id}:")
            for time_minutes in test_times:
                hour = time_minutes // 60
                minute = time_minutes % 60
                interval = system.get_impedance_interval(road_id, time_minutes)

                if interval:
                    print(f"  {hour:02d}:{minute:02d} - 阻抗区间: [{interval['lower']:.1f}, {interval['upper']:.1f}]秒")
                else:
                    print(f"  {hour:02d}:{minute:02d} - 无数据")

        print(f"\n=== 系统性能总结 ===")
        total_roads = len(system.road_segments)
        total_segments = sum(len(segments) for segments in system.road_segments.values())

        print(f"处理路段数量: {total_roads}")
        print(f"总时段数量: {total_segments}")
        print(f"平均每路段时段数: {total_segments/total_roads:.1f}")

        # 计算整体改进效果
        improvements = []
        for road_id in system.road_segments.keys():
            comparison = system.compare_with_traditional_method(road_id)
            if comparison and comparison['improvement']['width_reduction_percent'] > 0:
                improvements.append(comparison['improvement']['width_reduction_percent'])

        if improvements:
            print(f"平均区间宽度减少: {np.mean(improvements):.1f}%")
            print(f"最大区间宽度减少: {np.max(improvements):.1f}%")

        print("\n自适应系统构建完成！")

    except Exception as e:
        print(f"系统构建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
