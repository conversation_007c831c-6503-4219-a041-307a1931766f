============================================================
自适应时段区间阻抗系统分析报告
============================================================

1. 系统概览
------------------------------
处理路段数量: 67
原始数据记录数: 49816
数据时间范围: 2025-07-22 07:00:00 到 2025-08-04 09:00:00

2. 自适应划分统计
------------------------------
时段数量分布:
  5个时段: 0个路段 (0.0%)
  6个时段: 0个路段 (0.0%)
  7个时段: 66个路段 (98.5%)
  8个时段: 1个路段 (1.5%)
平均时段数量: 7.0
时段数量标准差: 0.1

3. 划分方法统计
------------------------------
  change_rate: 59个路段 (88.1%)
  adaptive_threshold: 8个路段 (11.9%)

4. 系统验证结果
------------------------------
有效路段数量: 1/67
平均时段数量: 7.0
平均F统计量: 0.11
平均质量评分: 126.41

5. 路段详细信息
------------------------------

路段 1-2:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 66.17
  07:00 - 07:10: 均值=208.5秒, 区间=[160.9, 269.7]秒
  07:10 - 07:30: 均值=218.6秒, 区间=[163.5, 285.1]秒
  07:30 - 07:50: 均值=232.2秒, 区间=[172.0, 338.0]秒
  07:50 - 08:00: 均值=245.7秒, 区间=[181.0, 347.4]秒
  08:00 - 08:30: 均值=228.2秒, 区间=[169.2, 329.3]秒
  08:30 - 08:40: 均值=232.5秒, 区间=[172.3, 340.4]秒
  08:40 - 09:00: 均值=244.2秒, 区间=[174.0, 343.8]秒

路段 1-9:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 166.59
  07:00 - 07:10: 均值=224.9秒, 区间=[176.7, 265.3]秒
  07:10 - 07:20: 均值=237.0秒, 区间=[200.2, 312.8]秒
  07:20 - 07:30: 均值=248.7秒, 区间=[197.7, 298.3]秒
  07:30 - 07:40: 均值=259.2秒, 区间=[198.4, 307.4]秒
  07:40 - 08:20: 均值=273.6秒, 区间=[207.7, 347.4]秒
  08:20 - 08:40: 均值=259.6秒, 区间=[215.4, 343.6]秒
  08:40 - 09:00: 均值=252.9秒, 区间=[224.0, 335.5]秒

路段 10-11:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 28.76
  07:00 - 07:10: 均值=183.9秒, 区间=[143.7, 227.9]秒
  07:10 - 07:20: 均值=188.4秒, 区间=[153.4, 225.8]秒
  07:20 - 07:30: 均值=193.2秒, 区间=[158.2, 236.0]秒
  07:30 - 07:40: 均值=197.2秒, 区间=[153.7, 231.0]秒
  07:40 - 07:50: 均值=207.3秒, 区间=[152.3, 255.7]秒
  07:50 - 08:00: 均值=204.2秒, 区间=[174.1, 248.1]秒
  08:00 - 09:00: 均值=203.3秒, 区间=[162.0, 255.0]秒

路段 10-18:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 61.87
  07:00 - 07:10: 均值=85.9秒, 区间=[63.0, 99.0]秒
  07:10 - 07:20: 均值=91.2秒, 区间=[74.7, 105.3]秒
  07:20 - 07:40: 均值=89.8秒, 区间=[66.5, 104.0]秒
  07:40 - 08:10: 均值=90.6秒, 区间=[75.0, 106.0]秒
  08:10 - 08:30: 均值=92.1秒, 区间=[76.1, 106.9]秒
  08:30 - 08:50: 均值=90.0秒, 区间=[78.9, 103.0]秒
  08:50 - 09:00: 均值=87.1秒, 区间=[78.0, 98.0]秒

路段 11-12:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 38.09
  07:00 - 07:20: 均值=251.4秒, 区间=[214.4, 286.6]秒
  07:20 - 07:30: 均值=259.7秒, 区间=[217.4, 293.9]秒
  07:30 - 07:40: 均值=265.1秒, 区间=[227.7, 318.6]秒
  07:40 - 07:50: 均值=272.6秒, 区间=[233.3, 308.9]秒
  07:50 - 08:40: 均值=274.5秒, 区间=[236.0, 332.9]秒
  08:40 - 08:50: 均值=266.7秒, 区间=[229.1, 325.6]秒
  08:50 - 09:00: 均值=260.2秒, 区间=[224.4, 328.0]秒

路段 11-19:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 141.44
  07:00 - 07:20: 均值=218.9秒, 区间=[185.0, 251.6]秒
  07:20 - 07:40: 均值=227.0秒, 区间=[195.3, 258.9]秒
  07:40 - 07:50: 均值=235.8秒, 区间=[200.4, 265.1]秒
  07:50 - 08:10: 均值=242.3秒, 区间=[207.3, 280.0]秒
  08:10 - 08:40: 均值=233.5秒, 区间=[185.7, 290.6]秒
  08:40 - 08:50: 均值=231.1秒, 区间=[191.3, 280.7]秒
  08:50 - 09:00: 均值=236.8秒, 区间=[197.3, 291.7]秒

路段 12-13:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 26.67
  07:00 - 07:10: 均值=166.8秒, 区间=[138.3, 203.7]秒
  07:10 - 07:20: 均值=174.8秒, 区间=[143.8, 204.8]秒
  07:20 - 07:40: 均值=172.3秒, 区间=[144.2, 208.6]秒
  07:40 - 07:50: 均值=175.5秒, 区间=[151.1, 202.1]秒
  07:50 - 08:00: 均值=174.8秒, 区间=[141.0, 206.4]秒
  08:00 - 08:10: 均值=177.3秒, 区间=[135.2, 212.4]秒
  08:10 - 09:00: 均值=170.0秒, 区间=[141.7, 208.0]秒

路段 12-20:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 35.96
  07:00 - 07:10: 均值=177.1秒, 区间=[162.6, 196.4]秒
  07:10 - 07:30: 均值=188.0秒, 区间=[161.9, 204.0]秒
  07:30 - 07:50: 均值=177.0秒, 区间=[161.4, 194.2]秒
  07:50 - 08:30: 均值=185.8秒, 区间=[161.5, 209.5]秒
  08:30 - 08:40: 均值=180.1秒, 区间=[168.6, 193.8]秒
  08:40 - 08:50: 均值=174.0秒, 区间=[161.4, 194.0]秒
  08:50 - 09:00: 均值=179.0秒, 区间=[160.0, 197.0]秒

路段 13-14:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 23.16
  07:00 - 07:30: 均值=103.2秒, 区间=[79.0, 131.9]秒
  07:30 - 07:40: 均值=107.1秒, 区间=[83.7, 133.3]秒
  07:40 - 08:10: 均值=103.7秒, 区间=[78.9, 129.8]秒
  08:10 - 08:20: 均值=107.7秒, 区间=[90.5, 127.9]秒
  08:20 - 08:30: 均值=103.2秒, 区间=[87.0, 135.0]秒
  08:30 - 08:50: 均值=108.6秒, 区间=[89.0, 137.1]秒
  08:50 - 09:00: 均值=106.9秒, 区间=[90.0, 129.8]秒

路段 13-21:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 52.73
  07:00 - 07:10: 均值=72.2秒, 区间=[53.4, 98.3]秒
  07:10 - 07:20: 均值=74.8秒, 区间=[59.5, 95.0]秒
  07:20 - 07:30: 均值=72.8秒, 区间=[50.6, 96.2]秒
  07:30 - 07:40: 均值=69.2秒, 区间=[53.5, 95.0]秒
  07:40 - 08:00: 均值=73.1秒, 区间=[55.1, 96.5]秒
  08:00 - 08:10: 均值=67.4秒, 区间=[52.0, 92.5]秒
  08:10 - 09:00: 均值=72.3秒, 区间=[53.0, 94.2]秒

路段 14-15:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 15.48
  07:00 - 07:30: 均值=219.6秒, 区间=[178.5, 273.2]秒
  07:30 - 07:40: 均值=229.1秒, 区间=[183.7, 266.9]秒
  07:40 - 07:50: 均值=221.5秒, 区间=[184.4, 275.9]秒
  07:50 - 08:00: 均值=227.8秒, 区间=[189.6, 273.9]秒
  08:00 - 08:10: 均值=223.8秒, 区间=[190.5, 264.0]秒
  08:10 - 08:20: 均值=230.3秒, 区间=[195.1, 267.4]秒
  08:20 - 09:00: 均值=223.9秒, 区间=[189.1, 269.0]秒

路段 14-22:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 30.99
  07:00 - 07:30: 均值=145.7秒, 区间=[110.6, 184.1]秒
  07:30 - 07:40: 均值=141.8秒, 区间=[107.9, 171.3]秒
  07:40 - 08:00: 均值=151.4秒, 区间=[114.7, 191.0]秒
  08:00 - 08:10: 均值=140.6秒, 区间=[107.5, 178.2]秒
  08:10 - 08:40: 均值=147.6秒, 区间=[109.8, 183.4]秒
  08:40 - 08:50: 均值=143.3秒, 区间=[108.4, 174.6]秒
  08:50 - 09:00: 均值=148.3秒, 区间=[109.5, 190.6]秒

路段 15-16:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 64.49
  07:00 - 07:10: 均值=68.4秒, 区间=[47.6, 95.0]秒
  07:10 - 07:20: 均值=71.1秒, 区间=[46.6, 93.0]秒
  07:20 - 07:30: 均值=74.3秒, 区间=[50.0, 99.5]秒
  07:30 - 08:10: 均值=75.3秒, 区间=[55.5, 97.5]秒
  08:10 - 08:20: 均值=78.6秒, 区间=[53.7, 97.6]秒
  08:20 - 08:40: 均值=74.2秒, 区间=[51.0, 96.6]秒
  08:40 - 09:00: 均值=77.5秒, 区间=[53.9, 98.4]秒

路段 15-23:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 35.17
  07:00 - 07:10: 均值=56.4秒, 区间=[35.2, 88.4]秒
  07:10 - 08:00: 均值=58.6秒, 区间=[37.1, 85.0]秒
  08:00 - 08:10: 均值=55.5秒, 区间=[43.0, 84.6]秒
  08:10 - 08:20: 均值=58.9秒, 区间=[40.7, 77.3]秒
  08:20 - 08:30: 均值=61.1秒, 区间=[42.5, 82.5]秒
  08:30 - 08:40: 均值=55.8秒, 区间=[39.4, 85.1]秒
  08:40 - 09:00: 均值=53.3秒, 区间=[36.0, 78.0]秒

路段 16-24:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 92.93
  07:00 - 07:10: 均值=195.4秒, 区间=[171.1, 224.0]秒
  07:10 - 07:20: 均值=200.9秒, 区间=[171.6, 221.6]秒
  07:20 - 07:40: 均值=205.7秒, 区间=[179.9, 235.0]秒
  07:40 - 08:00: 均值=203.1秒, 区间=[180.7, 229.6]秒
  08:00 - 08:10: 均值=199.5秒, 区间=[173.4, 234.5]秒
  08:10 - 08:20: 均值=204.3秒, 区间=[174.7, 228.3]秒
  08:20 - 09:00: 均值=200.9秒, 区间=[175.1, 228.9]秒

路段 17-18:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 104.58
  07:00 - 07:10: 均值=106.5秒, 区间=[70.4, 148.2]秒
  07:10 - 07:30: 均值=110.7秒, 区间=[72.2, 183.6]秒
  07:30 - 07:50: 均值=108.1秒, 区间=[67.1, 163.6]秒
  07:50 - 08:10: 均值=110.2秒, 区间=[75.2, 190.9]秒
  08:10 - 08:20: 均值=114.3秒, 区间=[78.6, 202.0]秒
  08:20 - 08:50: 均值=116.5秒, 区间=[77.4, 196.3]秒
  08:50 - 09:00: 均值=119.7秒, 区间=[80.9, 198.6]秒

路段 17-25:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 40.46
  07:00 - 07:10: 均值=273.6秒, 区间=[196.2, 426.5]秒
  07:10 - 07:20: 均值=284.3秒, 区间=[197.0, 425.8]秒
  07:20 - 07:30: 均值=277.9秒, 区间=[187.3, 409.8]秒
  07:30 - 07:40: 均值=289.9秒, 区间=[208.2, 409.0]秒
  07:40 - 08:10: 均值=293.2秒, 区间=[213.8, 412.0]秒
  08:10 - 08:20: 均值=303.1秒, 区间=[222.2, 457.8]秒
  08:20 - 09:00: 均值=296.3秒, 区间=[224.0, 421.9]秒

路段 18-19:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 79.64
  07:00 - 07:20: 均值=78.9秒, 区间=[59.1, 109.9]秒
  07:20 - 07:40: 均值=83.5秒, 区间=[63.4, 115.0]秒
  07:40 - 07:50: 均值=85.7秒, 区间=[66.2, 108.8]秒
  07:50 - 08:10: 均值=90.5秒, 区间=[66.0, 113.0]秒
  08:10 - 08:30: 均值=86.0秒, 区间=[61.3, 117.0]秒
  08:30 - 08:40: 均值=89.6秒, 区间=[64.5, 114.1]秒
  08:40 - 09:00: 均值=86.5秒, 区间=[63.0, 116.0]秒

路段 18-26:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 157.03
  07:00 - 07:10: 均值=101.6秒, 区间=[78.2, 133.2]秒
  07:10 - 07:20: 均值=104.3秒, 区间=[72.1, 132.1]秒
  07:20 - 07:30: 均值=103.2秒, 区间=[73.2, 133.0]秒
  07:30 - 07:50: 均值=109.6秒, 区间=[88.5, 137.0]秒
  07:50 - 08:00: 均值=111.8秒, 区间=[91.4, 145.4]秒
  08:00 - 08:10: 均值=110.1秒, 区间=[92.7, 136.3]秒
  08:10 - 09:00: 均值=112.8秒, 区间=[93.0, 147.1]秒

路段 19-20:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 42.39
  07:00 - 07:10: 均值=119.6秒, 区间=[91.4, 156.6]秒
  07:10 - 07:20: 均值=122.6秒, 区间=[99.5, 144.6]秒
  07:20 - 07:30: 均值=125.3秒, 区间=[96.7, 157.9]秒
  07:30 - 08:30: 均值=133.9秒, 区间=[104.9, 167.0]秒
  08:30 - 08:40: 均值=131.3秒, 区间=[93.0, 167.0]秒
  08:40 - 08:50: 均值=135.4秒, 区间=[110.4, 169.6]秒
  08:50 - 09:00: 均值=128.0秒, 区间=[107.0, 158.4]秒

路段 19-27:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 164.77
  07:00 - 07:20: 均值=79.8秒, 区间=[54.0, 109.9]秒
  07:20 - 07:30: 均值=85.1秒, 区间=[58.8, 111.4]秒
  07:30 - 07:40: 均值=86.7秒, 区间=[58.0, 112.3]秒
  07:40 - 08:30: 均值=89.2秒, 区间=[67.0, 117.0]秒
  08:30 - 08:40: 均值=81.4秒, 区间=[57.4, 108.6]秒
  08:40 - 08:50: 均值=84.1秒, 区间=[60.0, 111.6]秒
  08:50 - 09:00: 均值=82.7秒, 区间=[60.0, 108.6]秒

路段 2-10:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 391.54
  07:00 - 07:10: 均值=79.1秒, 区间=[61.2, 95.4]秒
  07:10 - 07:20: 均值=81.7秒, 区间=[59.2, 100.3]秒
  07:20 - 07:40: 均值=85.1秒, 区间=[66.0, 105.1]秒
  07:40 - 08:00: 均值=92.0秒, 区间=[65.2, 112.9]秒
  08:00 - 08:20: 均值=87.8秒, 区间=[70.0, 119.8]秒
  08:20 - 08:30: 均值=80.6秒, 区间=[71.0, 110.9]秒
  08:30 - 09:00: 均值=83.2秒, 区间=[69.0, 120.1]秒

路段 2-3:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 284.30
  07:00 - 07:10: 均值=236.7秒, 区间=[192.0, 287.3]秒
  07:10 - 07:20: 均值=233.7秒, 区间=[197.8, 279.7]秒
  07:20 - 07:30: 均值=231.8秒, 区间=[200.7, 276.4]秒
  07:30 - 07:40: 均值=244.9秒, 区间=[209.6, 287.2]秒
  07:40 - 08:00: 均值=248.1秒, 区间=[205.3, 283.8]秒
  08:00 - 08:30: 均值=256.4秒, 区间=[221.8, 292.0]秒
  08:30 - 09:00: 均值=251.6秒, 区间=[220.0, 293.1]秒

路段 20-21:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 79.58
  07:00 - 07:30: 均值=100.1秒, 区间=[77.0, 127.3]秒
  07:30 - 07:40: 均值=101.9秒, 区间=[83.4, 126.3]秒
  07:40 - 07:50: 均值=105.8秒, 区间=[84.6, 129.8]秒
  07:50 - 08:10: 均值=108.6秒, 区间=[89.2, 129.9]秒
  08:10 - 08:20: 均值=111.2秒, 区间=[95.6, 133.4]秒
  08:20 - 08:30: 均值=106.5秒, 区间=[89.5, 127.4]秒
  08:30 - 09:00: 均值=103.3秒, 区间=[88.0, 123.9]秒

路段 20-28:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 96.18
  07:00 - 07:10: 均值=141.0秒, 区间=[119.7, 180.6]秒
  07:10 - 07:20: 均值=144.2秒, 区间=[120.1, 173.2]秒
  07:20 - 07:40: 均值=142.3秒, 区间=[122.5, 163.5]秒
  07:40 - 08:00: 均值=147.8秒, 区间=[126.0, 176.9]秒
  08:00 - 08:20: 均值=151.7秒, 区间=[128.4, 181.0]秒
  08:20 - 08:40: 均值=149.0秒, 区间=[129.0, 181.0]秒
  08:40 - 09:00: 均值=147.1秒, 区间=[128.0, 177.0]秒

路段 21-22:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 0.00
  07:00 - 07:10: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:10 - 07:20: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:20 - 07:30: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:30 - 07:40: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:40 - 07:50: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:50 - 08:00: 均值=1.0秒, 区间=[1.0, 1.0]秒
  08:00 - 09:00: 均值=1.0秒, 区间=[1.0, 1.0]秒

路段 21-29:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 87.20
  07:00 - 07:40: 均值=205.5秒, 区间=[161.1, 258.4]秒
  07:40 - 07:50: 均值=210.2秒, 区间=[167.2, 260.0]秒
  07:50 - 08:20: 均值=221.3秒, 区间=[176.9, 272.2]秒
  08:20 - 08:30: 均值=217.4秒, 区间=[182.5, 258.1]秒
  08:30 - 08:40: 均值=209.0秒, 区间=[167.0, 261.0]秒
  08:40 - 08:50: 均值=214.5秒, 区间=[167.4, 256.1]秒
  08:50 - 09:00: 均值=208.0秒, 区间=[173.4, 261.8]秒

路段 22-23:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 32.47
  07:00 - 07:40: 均值=283.9秒, 区间=[217.4, 377.7]秒
  07:40 - 08:00: 均值=297.4秒, 区间=[231.4, 386.6]秒
  08:00 - 08:10: 均值=288.7秒, 区间=[241.9, 350.9]秒
  08:10 - 08:30: 均值=298.5秒, 区间=[241.7, 381.0]秒
  08:30 - 08:40: 均值=272.4秒, 区间=[231.5, 326.1]秒
  08:40 - 08:50: 均值=294.2秒, 区间=[239.2, 382.9]秒
  08:50 - 09:00: 均值=287.7秒, 区间=[236.1, 381.4]秒

路段 22-30:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 35.23
  07:00 - 07:20: 均值=252.1秒, 区间=[204.0, 298.0]秒
  07:20 - 07:30: 均值=262.4秒, 区间=[210.2, 305.0]秒
  07:30 - 07:40: 均值=256.1秒, 区间=[218.5, 298.0]秒
  07:40 - 08:20: 均值=264.1秒, 区间=[214.0, 319.9]秒
  08:20 - 08:30: 均值=271.1秒, 区间=[226.2, 326.6]秒
  08:30 - 08:40: 均值=249.2秒, 区间=[207.0, 282.5]秒
  08:40 - 09:00: 均值=258.7秒, 区间=[213.0, 321.9]秒

路段 23-24:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 21.69
  07:00 - 07:20: 均值=172.4秒, 区间=[145.0, 203.8]秒
  07:20 - 08:00: 均值=180.3秒, 区间=[153.5, 213.0]秒
  08:00 - 08:10: 均值=174.6秒, 区间=[140.7, 205.4]秒
  08:10 - 08:20: 均值=178.1秒, 区间=[154.6, 213.1]秒
  08:20 - 08:30: 均值=174.4秒, 区间=[149.0, 205.4]秒
  08:30 - 08:40: 均值=177.8秒, 区间=[146.8, 210.0]秒
  08:40 - 09:00: 均值=180.2秒, 区间=[150.2, 211.8]秒

路段 23-31:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 72.60
  07:00 - 07:10: 均值=72.6秒, 区间=[38.0, 119.9]秒
  07:10 - 07:20: 均值=75.0秒, 区间=[32.0, 125.0]秒
  07:20 - 07:50: 均值=77.2秒, 区间=[42.4, 121.3]秒
  07:50 - 08:00: 均值=81.2秒, 区间=[43.2, 119.8]秒
  08:00 - 08:20: 均值=82.6秒, 区间=[48.4, 123.3]秒
  08:20 - 08:40: 均值=73.6秒, 区间=[47.0, 107.0]秒
  08:40 - 09:00: 均值=76.9秒, 区间=[52.0, 118.4]秒

路段 24-32:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 103.45
  07:00 - 07:10: 均值=70.5秒, 区间=[63.0, 81.3]秒
  07:10 - 07:20: 均值=75.6秒, 区间=[62.4, 92.4]秒
  07:20 - 07:40: 均值=79.2秒, 区间=[61.0, 100.0]秒
  07:40 - 07:50: 均值=83.1秒, 区间=[61.3, 103.7]秒
  07:50 - 08:20: 均值=84.6秒, 区间=[60.0, 104.0]秒
  08:20 - 08:30: 均值=81.8秒, 区间=[62.3, 93.7]秒
  08:30 - 09:00: 均值=79.1秒, 区间=[64.0, 92.7]秒

路段 25-26:
  时段数量: 8 (目标: 8)
  划分方法: change_rate
  质量评分: 2277.56
  07:00 - 07:10: 均值=85.0秒, 区间=[55.1, 162.7]秒
  07:10 - 07:20: 均值=89.3秒, 区间=[69.0, 141.8]秒
  07:20 - 07:40: 均值=94.4秒, 区间=[60.5, 155.1]秒
  07:40 - 08:10: 均值=100.1秒, 区间=[71.0, 162.4]秒
  08:10 - 08:30: 均值=104.6秒, 区间=[69.0, 187.0]秒
  08:30 - 08:40: 均值=100.3秒, 区间=[72.0, 161.3]秒
  08:40 - 09:00: 均值=97.1秒, 区间=[71.7, 183.3]秒
  19:00 - 19:10: 均值=152.5秒, 区间=[138.7, 166.3]秒

路段 25-33:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 279.88
  07:00 - 07:10: 均值=316.5秒, 区间=[238.7, 409.6]秒
  07:10 - 07:40: 均值=329.9秒, 区间=[268.0, 408.4]秒
  07:40 - 08:00: 均值=348.8秒, 区间=[273.2, 440.8]秒
  08:00 - 08:30: 均值=335.7秒, 区间=[254.0, 441.5]秒
  08:30 - 08:50: 均值=323.5秒, 区间=[254.9, 442.1]秒
  08:50 - 09:00: 均值=316.0秒, 区间=[253.6, 419.6]秒
  19:00 - 19:10: 均值=389.0秒, 区间=[388.1, 389.9]秒

路段 26-27:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 74.51
  07:00 - 07:10: 均值=124.1秒, 区间=[106.0, 136.0]秒
  07:10 - 07:20: 均值=127.6秒, 区间=[106.8, 141.6]秒
  07:20 - 08:20: 均值=133.3秒, 区间=[109.4, 151.0]秒
  08:20 - 08:30: 均值=129.4秒, 区间=[113.7, 147.7]秒
  08:30 - 08:40: 均值=126.1秒, 区间=[112.5, 147.9]秒
  08:40 - 08:50: 均值=122.3秒, 区间=[108.3, 141.3]秒
  08:50 - 09:00: 均值=119.9秒, 区间=[110.0, 140.5]秒

路段 26-34:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 26.35
  07:00 - 07:10: 均值=81.9秒, 区间=[65.3, 94.7]秒
  07:10 - 07:30: 均值=84.2秒, 区间=[69.0, 97.5]秒
  07:30 - 07:40: 均值=87.0秒, 区间=[74.4, 100.2]秒
  07:40 - 08:00: 均值=88.0秒, 区间=[72.2, 100.9]秒
  08:00 - 08:10: 均值=84.7秒, 区间=[74.0, 97.0]秒
  08:10 - 08:30: 均值=86.0秒, 区间=[72.0, 98.0]秒
  08:30 - 09:00: 均值=82.4秒, 区间=[73.2, 94.0]秒

路段 27-28:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 156.69
  07:00 - 07:10: 均值=488.1秒, 区间=[428.9, 536.9]秒
  07:10 - 07:30: 均值=498.3秒, 区间=[440.7, 545.6]秒
  07:30 - 07:40: 均值=517.5秒, 区间=[451.2, 584.2]秒
  07:40 - 08:20: 均值=529.0秒, 区间=[452.8, 611.0]秒
  08:20 - 08:30: 均值=524.0秒, 区间=[459.0, 613.1]秒
  08:30 - 08:50: 均值=512.9秒, 区间=[452.9, 630.8]秒
  08:50 - 09:00: 均值=505.8秒, 区间=[454.6, 613.7]秒

路段 27-35:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 64.97
  07:00 - 07:20: 均值=293.5秒, 区间=[232.1, 380.6]秒
  07:20 - 07:30: 均值=299.3秒, 区间=[237.0, 367.3]秒
  07:30 - 07:50: 均值=312.1秒, 区间=[264.0, 373.1]秒
  07:50 - 08:20: 均值=318.7秒, 区间=[267.0, 389.2]秒
  08:20 - 08:30: 均值=311.7秒, 区间=[255.0, 379.0]秒
  08:30 - 08:40: 均值=299.8秒, 区间=[253.5, 392.3]秒
  08:40 - 09:00: 均值=295.0秒, 区间=[262.4, 382.2]秒

路段 28-29:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 168.82
  07:00 - 07:30: 均值=232.9秒, 区间=[185.8, 280.9]秒
  07:30 - 07:50: 均值=237.8秒, 区间=[193.8, 294.1]秒
  07:50 - 08:00: 均值=249.1秒, 区间=[196.0, 292.7]秒
  08:00 - 08:10: 均值=252.9秒, 区间=[213.9, 296.8]秒
  08:10 - 08:30: 均值=250.7秒, 区间=[210.7, 322.1]秒
  08:30 - 08:50: 均值=242.6秒, 区间=[195.9, 309.3]秒
  08:50 - 09:00: 均值=236.9秒, 区间=[208.2, 281.1]秒

路段 28-36:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 40.48
  07:00 - 07:30: 均值=241.7秒, 区间=[188.7, 296.4]秒
  07:30 - 07:40: 均值=252.9秒, 区间=[204.6, 326.0]秒
  07:40 - 07:50: 均值=260.4秒, 区间=[211.1, 345.6]秒
  07:50 - 08:00: 均值=268.7秒, 区间=[202.7, 357.4]秒
  08:00 - 08:10: 均值=276.2秒, 区间=[204.0, 361.4]秒
  08:10 - 08:30: 均值=256.3秒, 区间=[193.2, 357.7]秒
  08:30 - 09:00: 均值=251.4秒, 区间=[201.6, 350.4]秒

路段 29-30:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 93.96
  07:00 - 07:10: 均值=86.1秒, 区间=[76.0, 103.3]秒
  07:10 - 07:30: 均值=86.7秒, 区间=[73.2, 103.8]秒
  07:30 - 07:40: 均值=89.1秒, 区间=[76.4, 104.6]秒
  07:40 - 08:00: 均值=90.5秒, 区间=[76.9, 104.0]秒
  08:00 - 08:20: 均值=88.9秒, 区间=[77.3, 100.0]秒
  08:20 - 08:40: 均值=87.7秒, 区间=[79.0, 101.0]秒
  08:40 - 09:00: 均值=86.7秒, 区间=[78.8, 99.2]秒

路段 29-37:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 106.31
  07:00 - 07:20: 均值=92.7秒, 区间=[52.1, 136.9]秒
  07:20 - 07:30: 均值=99.7秒, 区间=[67.0, 135.8]秒
  07:30 - 07:50: 均值=98.3秒, 区间=[65.3, 139.0]秒
  07:50 - 08:00: 均值=104.2秒, 区间=[68.7, 152.3]秒
  08:00 - 08:10: 均值=109.9秒, 区间=[74.2, 147.0]秒
  08:10 - 08:40: 均值=103.9秒, 区间=[62.0, 163.8]秒
  08:40 - 09:00: 均值=108.6秒, 区间=[66.0, 166.0]秒

路段 3-11:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 25.39
  07:00 - 07:10: 均值=195.2秒, 区间=[155.4, 245.6]秒
  07:10 - 07:20: 均值=197.0秒, 区间=[161.0, 237.6]秒
  07:20 - 07:30: 均值=198.8秒, 区间=[166.4, 235.6]秒
  07:30 - 07:40: 均值=201.7秒, 区间=[165.2, 241.3]秒
  07:40 - 08:00: 均值=208.4秒, 区间=[168.5, 246.0]秒
  08:00 - 08:10: 均值=211.9秒, 区间=[178.7, 248.5]秒
  08:10 - 09:00: 均值=208.1秒, 区间=[170.7, 254.8]秒

路段 3-4:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 223.65
  07:00 - 07:10: 均值=235.5秒, 区间=[200.4, 283.6]秒
  07:10 - 07:30: 均值=237.4秒, 区间=[205.4, 275.3]秒
  07:30 - 07:40: 均值=242.9秒, 区间=[209.6, 285.8]秒
  07:40 - 07:50: 均值=247.2秒, 区间=[203.5, 278.0]秒
  07:50 - 08:00: 均值=244.0秒, 区间=[206.4, 277.3]秒
  08:00 - 08:30: 均值=256.0秒, 区间=[227.7, 288.6]秒
  08:30 - 09:00: 均值=248.6秒, 区间=[215.0, 286.4]秒

路段 30-31:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 124.38
  07:00 - 07:10: 均值=152.3秒, 区间=[121.0, 184.8]秒
  07:10 - 07:20: 均值=154.7秒, 区间=[120.6, 186.0]秒
  07:20 - 07:30: 均值=158.3秒, 区间=[125.7, 194.4]秒
  07:30 - 07:40: 均值=159.9秒, 区间=[130.7, 186.4]秒
  07:40 - 07:50: 均值=163.0秒, 区间=[127.0, 189.3]秒
  07:50 - 08:00: 均值=165.0秒, 区间=[124.7, 192.3]秒
  08:00 - 09:00: 均值=169.3秒, 区间=[148.0, 192.3]秒

路段 30-38:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 163.74
  07:00 - 07:10: 均值=128.3秒, 区间=[101.0, 165.9]秒
  07:10 - 07:40: 均值=134.5秒, 区间=[99.0, 171.2]秒
  07:40 - 08:00: 均值=139.9秒, 区间=[107.9, 172.0]秒
  08:00 - 08:30: 均值=148.5秒, 区间=[127.5, 173.5]秒
  08:30 - 08:40: 均值=144.0秒, 区间=[126.9, 171.6]秒
  08:40 - 08:50: 均值=146.4秒, 区间=[125.5, 166.0]秒
  08:50 - 09:00: 均值=142.9秒, 区间=[125.8, 173.5]秒

路段 31-32:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 201.12
  07:00 - 07:20: 均值=87.8秒, 区间=[67.0, 103.9]秒
  07:20 - 07:30: 均值=91.4秒, 区间=[71.1, 104.6]秒
  07:30 - 07:40: 均值=97.3秒, 区间=[81.7, 113.8]秒
  07:40 - 08:00: 均值=99.8秒, 区间=[75.0, 119.0]秒
  08:00 - 08:30: 均值=96.3秒, 区间=[76.0, 118.5]秒
  08:30 - 08:50: 均值=94.2秒, 区间=[78.8, 110.0]秒
  08:50 - 09:00: 均值=91.8秒, 区间=[74.4, 112.6]秒

路段 31-39:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 25.07
  07:00 - 07:20: 均值=55.9秒, 区间=[37.0, 76.1]秒
  07:20 - 07:30: 均值=53.1秒, 区间=[38.2, 74.8]秒
  07:30 - 07:50: 均值=57.7秒, 区间=[38.2, 82.8]秒
  07:50 - 08:20: 均值=56.8秒, 区间=[40.6, 81.0]秒
  08:20 - 08:40: 均值=54.8秒, 区间=[39.5, 80.0]秒
  08:40 - 08:50: 均值=59.6秒, 区间=[40.8, 76.6]秒
  08:50 - 09:00: 均值=53.3秒, 区间=[36.0, 80.6]秒

路段 32-40:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 24.76
  07:00 - 07:10: 均值=53.8秒, 区间=[43.3, 64.0]秒
  07:10 - 07:40: 均值=55.4秒, 区间=[48.0, 74.0]秒
  07:40 - 08:20: 均值=56.1秒, 区间=[47.0, 75.0]秒
  08:20 - 08:30: 均值=55.3秒, 区间=[48.6, 69.8]秒
  08:30 - 08:40: 均值=53.4秒, 区间=[45.3, 62.0]秒
  08:40 - 08:50: 均值=51.9秒, 区间=[45.0, 60.0]秒
  08:50 - 09:00: 均值=49.6秒, 区间=[45.0, 54.5]秒

路段 33-34:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 94.17
  07:00 - 07:10: 均值=205.6秒, 区间=[156.5, 232.9]秒
  07:10 - 07:20: 均值=214.5秒, 区间=[169.4, 240.6]秒
  07:20 - 07:40: 均值=221.4秒, 区间=[182.1, 257.0]秒
  07:40 - 08:00: 均值=236.9秒, 区间=[192.0, 318.6]秒
  08:00 - 08:20: 均值=230.7秒, 区间=[178.0, 291.0]秒
  08:20 - 08:40: 均值=227.2秒, 区间=[178.0, 305.5]秒
  08:40 - 09:00: 均值=211.7秒, 区间=[180.0, 279.4]秒

路段 34-35:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 125.06
  07:00 - 07:10: 均值=415.3秒, 区间=[355.9, 471.4]秒
  07:10 - 07:20: 均值=420.8秒, 区间=[376.8, 469.9]秒
  07:20 - 07:30: 均值=426.3秒, 区间=[377.2, 484.4]秒
  07:30 - 07:40: 均值=433.4秒, 区间=[361.0, 490.0]秒
  07:40 - 08:20: 均值=451.6秒, 区间=[389.9, 526.0]秒
  08:20 - 08:30: 均值=441.6秒, 区间=[389.7, 522.1]秒
  08:30 - 09:00: 均值=424.1秒, 区间=[373.2, 500.8]秒

路段 35-36:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 292.48
  07:00 - 07:10: 均值=113.7秒, 区间=[82.0, 162.2]秒
  07:10 - 07:30: 均值=119.9秒, 区间=[98.1, 149.0]秒
  07:30 - 07:40: 均值=126.2秒, 区间=[103.0, 169.0]秒
  07:40 - 08:00: 均值=133.9秒, 区间=[108.7, 169.1]秒
  08:00 - 08:20: 均值=139.8秒, 区间=[107.0, 175.8]秒
  08:20 - 08:30: 均值=127.5秒, 区间=[100.5, 169.9]秒
  08:30 - 09:00: 均值=133.7秒, 区间=[107.9, 178.3]秒

路段 36-37:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 48.25
  07:00 - 07:10: 均值=228.6秒, 区间=[169.8, 291.2]秒
  07:10 - 07:20: 均值=231.9秒, 区间=[159.0, 316.6]秒
  07:20 - 07:40: 均值=237.5秒, 区间=[189.8, 291.2]秒
  07:40 - 07:50: 均值=252.1秒, 区间=[187.0, 366.9]秒
  07:50 - 08:00: 均值=254.1秒, 区间=[197.8, 372.2]秒
  08:00 - 08:10: 均值=263.1秒, 区间=[198.7, 343.0]秒
  08:10 - 09:00: 均值=252.4秒, 区间=[185.8, 382.9]秒

路段 37-38:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 189.59
  07:00 - 07:20: 均值=212.9秒, 区间=[163.1, 267.0]秒
  07:20 - 07:30: 均值=223.3秒, 区间=[163.4, 277.8]秒
  07:30 - 07:50: 均值=221.5秒, 区间=[171.1, 277.0]秒
  07:50 - 08:00: 均值=228.6秒, 区间=[180.3, 287.4]秒
  08:00 - 08:10: 均值=261.2秒, 区间=[195.3, 306.3]秒
  08:10 - 08:20: 均值=244.7秒, 区间=[185.4, 302.7]秒
  08:20 - 19:10: 均值=221.4秒, 区间=[183.3, 269.2]秒

路段 38-39:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 52.52
  07:00 - 07:20: 均值=192.6秒, 区间=[163.1, 229.7]秒
  07:20 - 07:40: 均值=200.5秒, 区间=[163.4, 232.3]秒
  07:40 - 08:20: 均值=204.8秒, 区间=[165.0, 241.8]秒
  08:20 - 08:40: 均值=192.9秒, 区间=[160.0, 233.0]秒
  08:40 - 08:50: 均值=199.4秒, 区间=[156.3, 241.4]秒
  08:50 - 09:00: 均值=191.9秒, 区间=[155.5, 217.5]秒
  19:00 - 19:10: 均值=181.0秒, 区间=[173.4, 188.6]秒

路段 39-40:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 112.18
  07:00 - 07:10: 均值=170.5秒, 区间=[139.0, 201.7]秒
  07:10 - 07:30: 均值=177.5秒, 区间=[142.1, 208.9]秒
  07:30 - 07:40: 均值=183.0秒, 区间=[155.4, 219.2]秒
  07:40 - 07:50: 均值=194.5秒, 区间=[151.0, 233.3]秒
  07:50 - 08:00: 均值=199.0秒, 区间=[158.1, 239.0]秒
  08:00 - 08:30: 均值=189.3秒, 区间=[150.5, 232.5]秒
  08:30 - 19:10: 均值=178.1秒, 区间=[149.4, 213.0]秒

路段 4-12:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 15.22
  07:00 - 07:10: 均值=64.9秒, 区间=[57.0, 72.0]秒
  07:10 - 07:20: 均值=66.7秒, 区间=[58.0, 79.0]秒
  07:20 - 07:30: 均值=69.6秒, 区间=[62.0, 74.8]秒
  07:30 - 08:00: 均值=68.4秒, 区间=[60.0, 77.0]秒
  08:00 - 08:40: 均值=68.1秒, 区间=[56.2, 78.0]秒
  08:40 - 08:50: 均值=62.9秒, 区间=[54.3, 75.0]秒
  08:50 - 19:10: 均值=68.3秒, 区间=[60.0, 76.4]秒

路段 4-5:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 36.73
  07:00 - 07:10: 均值=72.7秒, 区间=[59.7, 91.9]秒
  07:10 - 07:40: 均值=77.8秒, 区间=[56.0, 105.0]秒
  07:40 - 08:10: 均值=80.6秒, 区间=[60.1, 104.8]秒
  08:10 - 08:40: 均值=76.3秒, 区间=[58.7, 101.3]秒
  08:40 - 08:50: 均值=74.0秒, 区间=[56.7, 95.0]秒
  08:50 - 09:00: 均值=82.4秒, 区间=[64.5, 103.5]秒
  19:00 - 19:10: 均值=78.5秒, 区间=[78.0, 79.0]秒

路段 5-13:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 193.35
  07:00 - 07:10: 均值=178.6秒, 区间=[145.1, 224.1]秒
  07:10 - 07:40: 均值=184.8秒, 区间=[146.8, 226.0]秒
  07:40 - 07:50: 均值=192.4秒, 区间=[154.9, 236.2]秒
  07:50 - 08:00: 均值=188.9秒, 区间=[148.0, 231.3]秒
  08:00 - 08:40: 均值=178.8秒, 区间=[141.3, 216.7]秒
  08:40 - 09:00: 均值=174.5秒, 区间=[136.2, 213.9]秒
  19:00 - 19:10: 均值=186.7秒, 区间=[159.9, 220.8]秒

路段 5-6:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 32.03
  07:00 - 07:10: 均值=59.4秒, 区间=[41.0, 91.3]秒
  07:10 - 07:30: 均值=60.5秒, 区间=[43.0, 85.3]秒
  07:30 - 08:00: 均值=65.1秒, 区间=[44.7, 93.3]秒
  08:00 - 08:20: 均值=63.2秒, 区间=[42.6, 87.4]秒
  08:20 - 08:30: 均值=61.1秒, 区间=[44.9, 88.3]秒
  08:30 - 09:00: 均值=62.7秒, 区间=[41.6, 91.0]秒
  19:00 - 19:10: 均值=65.3秒, 区间=[50.6, 82.9]秒

路段 6-14:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 25.88
  07:00 - 07:10: 均值=61.3秒, 区间=[48.7, 74.0]秒
  07:10 - 08:00: 均值=62.7秒, 区间=[52.0, 74.0]秒
  08:00 - 08:10: 均值=59.3秒, 区间=[52.0, 69.3]秒
  08:10 - 08:20: 均值=61.5秒, 区间=[53.0, 75.0]秒
  08:20 - 08:30: 均值=63.3秒, 区间=[46.4, 74.6]秒
  08:30 - 08:40: 均值=61.3秒, 区间=[52.5, 72.0]秒
  08:40 - 19:10: 均值=56.8秒, 区间=[49.0, 69.7]秒

路段 6-7:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 104.61
  07:00 - 07:10: 均值=70.2秒, 区间=[53.4, 94.7]秒
  07:10 - 07:30: 均值=71.3秒, 区间=[50.0, 99.7]秒
  07:30 - 07:40: 均值=73.7秒, 区间=[55.8, 93.2]秒
  07:40 - 08:00: 均值=71.2秒, 区间=[52.0, 97.0]秒
  08:00 - 08:20: 均值=76.3秒, 区间=[55.3, 98.0]秒
  08:20 - 09:00: 均值=71.7秒, 区间=[53.1, 95.9]秒
  19:00 - 19:10: 均值=78.0秒, 区间=[77.0, 79.0]秒

路段 7-15:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 11.21
  07:00 - 07:10: 均值=50.2秒, 区间=[33.6, 79.4]秒
  07:10 - 07:50: 均值=53.2秒, 区间=[32.0, 80.5]秒
  07:50 - 08:20: 均值=55.5秒, 区间=[35.0, 80.2]秒
  08:20 - 08:30: 均值=49.7秒, 区间=[32.0, 74.0]秒
  08:30 - 08:40: 均值=53.8秒, 区间=[35.0, 75.4]秒
  08:40 - 09:00: 均值=50.8秒, 区间=[31.0, 72.0]秒
  19:00 - 19:10: 均值=44.5秒, 区间=[38.3, 50.7]秒

路段 7-8:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 54.26
  07:00 - 07:20: 均值=257.8秒, 区间=[216.1, 298.9]秒
  07:20 - 07:30: 均值=249.8秒, 区间=[219.2, 278.0]秒
  07:30 - 07:40: 均值=261.3秒, 区间=[219.7, 303.7]秒
  07:40 - 07:50: 均值=265.7秒, 区间=[219.6, 313.9]秒
  07:50 - 08:20: 均值=254.4秒, 区间=[212.0, 296.4]秒
  08:20 - 08:40: 均值=257.1秒, 区间=[219.1, 297.8]秒
  08:40 - 19:10: 均值=251.2秒, 区间=[216.0, 289.0]秒

路段 8-16:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 213.97
  07:00 - 07:20: 均值=273.6秒, 区间=[245.0, 304.8]秒
  07:20 - 07:50: 均值=277.7秒, 区间=[250.7, 301.6]秒
  07:50 - 08:00: 均值=281.0秒, 区间=[251.6, 308.8]秒
  08:00 - 08:10: 均值=275.4秒, 区间=[254.0, 295.4]秒
  08:10 - 08:20: 均值=278.7秒, 区间=[256.4, 304.8]秒
  08:20 - 09:00: 均值=275.4秒, 区间=[254.0, 301.6]秒
  19:00 - 19:10: 均值=261.5秒, 区间=[257.2, 265.8]秒

路段 9-10:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 67.64
  07:00 - 07:10: 均值=104.9秒, 区间=[85.7, 119.0]秒
  07:10 - 07:30: 均值=109.7秒, 区间=[80.7, 132.0]秒
  07:30 - 07:40: 均值=118.4秒, 区间=[89.2, 146.1]秒
  07:40 - 07:50: 均值=122.3秒, 区间=[96.7, 156.1]秒
  07:50 - 08:00: 均值=126.9秒, 区间=[91.0, 160.7]秒
  08:00 - 08:30: 均值=121.4秒, 区间=[91.6, 165.0]秒
  08:30 - 09:00: 均值=113.2秒, 区间=[97.1, 151.4]秒

路段 9-17:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 49.02
  07:00 - 07:30: 均值=392.7秒, 区间=[314.0, 455.5]秒
  07:30 - 07:40: 均值=404.2秒, 区间=[327.9, 464.8]秒
  07:40 - 07:50: 均值=422.4秒, 区间=[346.4, 485.3]秒
  07:50 - 08:10: 均值=432.5秒, 区间=[357.5, 513.9]秒
  08:10 - 08:20: 均值=442.3秒, 区间=[357.8, 529.9]秒
  08:20 - 08:30: 均值=423.2秒, 区间=[360.4, 514.0]秒
  08:30 - 09:00: 均值=404.9秒, 区间=[352.0, 521.1]秒
