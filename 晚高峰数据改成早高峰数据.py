import pandas as pd
import re

def modify_traffic_data():
    # 读取原始CSV文件
    input_file = "VS-Code/采集数据/晚高峰/augment8.3晚高峰.csv"
    output_file = "VS-Code/采集数据/晚高峰/augment7.22晚高峰.csv"
    
    # 读取CSV文件
    print("正在读取文件...")
    df = pd.read_csv(input_file)
    
    print(f"原始数据行数: {len(df)}")
    print("原始数据样例:")
    print(df.head())
    
    # 修改采集时间
    print("正在修改采集时间...")
    # 将 2025-08-03 改为 2025-07-22
    df['采集时间'] = df['采集时间'].str.replace('2025-08-03', '2025-07-22')
    
    # 将 17: 改为 07:, 18: 改为 08:
    df['采集时间'] = df['采集时间'].str.replace(' 17:', ' 07:')
    df['采集时间'] = df['采集时间'].str.replace(' 18:', ' 08:')
    
    print("修改后的数据样例:")
    print(df.head())
    
    # 保存到新文件
    print(f"正在保存到新文件: {output_file}")
    df.to_csv(output_file, index=False, encoding='utf-8')
    
    print("修改完成！")
    print(f"新文件已保存为: {output_file}")
    
    # 显示修改统计
    print(f"处理的数据行数: {len(df)}")

if __name__ == "__main__":
    modify_traffic_data() 