#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时段区间阻抗系统实现
基于历史交通数据的时段区间阻抗划分方法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class TrafficDataPreprocessor:
    """交通数据预处理器"""
    
    def __init__(self):
        self.data = None
        self.cleaned_data = None
        
    def load_data(self, file_paths):
        """加载多个CSV文件的交通数据"""
        all_data = []
        
        for file_path in file_paths:
            try:
                df = pd.read_csv(file_path, encoding='utf-8')
                all_data.append(df)
                print(f"成功加载文件: {file_path}, 记录数: {len(df)}")
            except Exception as e:
                print(f"加载文件失败 {file_path}: {e}")
                
        if all_data:
            self.data = pd.concat(all_data, ignore_index=True)
            print(f"总计加载记录数: {len(self.data)}")
        else:
            raise ValueError("没有成功加载任何数据文件")
            
    def clean_data(self):
        """数据清洗和预处理"""
        if self.data is None:
            raise ValueError("请先加载数据")
            
        self.cleaned_data = self.data.copy()
        
        # 1. 处理时间字段
        self.cleaned_data['采集时间'] = pd.to_datetime(self.cleaned_data['采集时间'])
        
        # 2. 提取时间特征
        self.cleaned_data['小时'] = self.cleaned_data['采集时间'].dt.hour
        self.cleaned_data['分钟'] = self.cleaned_data['采集时间'].dt.minute
        self.cleaned_data['星期'] = self.cleaned_data['采集时间'].dt.weekday
        self.cleaned_data['时间分钟'] = self.cleaned_data['小时'] * 60 + self.cleaned_data['分钟']
        
        # 3. 计算速度 (米/秒)
        self.cleaned_data['速度'] = self.cleaned_data['距离(米)'] / self.cleaned_data['通行时间(秒)']
        
        # 4. 异常值检测和处理 (使用IQR方法)
        for road_id in self.cleaned_data['路段ID'].unique():
            road_mask = self.cleaned_data['路段ID'] == road_id
            road_data = self.cleaned_data[road_mask]['通行时间(秒)']
            
            Q1 = road_data.quantile(0.25)
            Q3 = road_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 标记异常值
            outlier_mask = road_mask & (
                (self.cleaned_data['通行时间(秒)'] < lower_bound) | 
                (self.cleaned_data['通行时间(秒)'] > upper_bound)
            )
            
            outlier_count = outlier_mask.sum()
            if outlier_count > 0:
                print(f"路段 {road_id}: 检测到 {outlier_count} 个异常值")
                
        # 移除异常值
        valid_mask = True
        for road_id in self.cleaned_data['路段ID'].unique():
            road_mask = self.cleaned_data['路段ID'] == road_id
            road_data = self.cleaned_data[road_mask]['通行时间(秒)']
            
            Q1 = road_data.quantile(0.25)
            Q3 = road_data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            road_valid_mask = ~road_mask | (
                (self.cleaned_data['通行时间(秒)'] >= lower_bound) & 
                (self.cleaned_data['通行时间(秒)'] <= upper_bound)
            )
            valid_mask = valid_mask & road_valid_mask
            
        self.cleaned_data = self.cleaned_data[valid_mask].reset_index(drop=True)
        print(f"清洗后数据记录数: {len(self.cleaned_data)}")
        
    def aggregate_by_time_intervals(self, interval_minutes=10):
        """按时间间隔聚合数据 - 使用更小的时间间隔以获得更多时段"""
        if self.cleaned_data is None:
            raise ValueError("请先进行数据清洗")

        # 计算时间间隔索引
        self.cleaned_data['时间间隔'] = self.cleaned_data['时间分钟'] // interval_minutes

        # 按路段和时间间隔聚合
        aggregated = self.cleaned_data.groupby(['路段ID', '时间间隔']).agg({
            '通行时间(秒)': ['mean', 'std', 'min', 'max', 'count'],
            '速度': ['mean', 'std'],
            '距离(米)': 'first'
        }).reset_index()

        # 扁平化列名
        aggregated.columns = [
            '路段ID', '时间间隔',
            '通行时间_均值', '通行时间_标准差', '通行时间_最小值', '通行时间_最大值', '样本数量',
            '速度_均值', '速度_标准差', '距离'
        ]

        # 降低样本数量要求，以保留更多时间间隔
        aggregated = aggregated[aggregated['样本数量'] >= 2].reset_index(drop=True)

        return aggregated

class TimeSegmentPartitioner:
    """时段划分器"""
    
    def __init__(self, min_samples_per_segment=5):
        self.min_samples_per_segment = min_samples_per_segment
        self.segments = {}
        
    def partition_by_statistical_test(self, road_data, significance_level=0.15, target_segments=6):
        """基于统计检验的时段划分方法 - 优化版本"""
        time_intervals = sorted(road_data['时间间隔'].unique())

        if len(time_intervals) < 2:
            return [time_intervals]

        # 如果时间间隔数量少于目标时段数，直接返回每个间隔作为一个时段
        if len(time_intervals) <= target_segments:
            return [[interval] for interval in time_intervals]

        # 方法1：基于变化率的时段划分
        segments = self._partition_by_change_rate(road_data, target_segments)

        # 如果时段数量仍然不足，使用更敏感的方法
        if len(segments) < target_segments:
            segments = self._partition_by_adaptive_threshold(road_data, target_segments)

        return segments

    def _partition_by_change_rate(self, road_data, target_segments):
        """基于变化率的时段划分"""
        time_intervals = sorted(road_data['时间间隔'].unique())

        if len(time_intervals) < 2:
            return [time_intervals]

        # 计算相邻时间间隔的通行时间变化率
        changes = []
        means = []

        for interval in time_intervals:
            interval_data = road_data[road_data['时间间隔'] == interval]
            if len(interval_data) > 0:
                means.append(interval_data['通行时间_均值'].iloc[0])
            else:
                means.append(0)

        # 计算变化率
        for i in range(1, len(means)):
            if means[i-1] != 0:
                change_rate = abs((means[i] - means[i-1]) / means[i-1])
            else:
                change_rate = 0
            changes.append((i, change_rate))

        # 按变化率排序，选择变化最大的点作为分割点
        changes.sort(key=lambda x: x[1], reverse=True)

        # 选择前(target_segments-1)个变化点
        split_points = sorted([change[0] for change in changes[:target_segments-1]])

        # 构建时段
        segments = []
        start_idx = 0

        for split_point in split_points:
            if split_point > start_idx:
                segments.append(time_intervals[start_idx:split_point])
                start_idx = split_point

        # 添加最后一个时段
        if start_idx < len(time_intervals):
            segments.append(time_intervals[start_idx:])

        return [seg for seg in segments if len(seg) > 0]

    def _partition_by_adaptive_threshold(self, road_data, target_segments):
        """基于自适应阈值的时段划分"""
        time_intervals = sorted(road_data['时间间隔'].unique())

        if len(time_intervals) < 2:
            return [time_intervals]

        # 获取所有时间间隔的通行时间均值
        means = []
        for interval in time_intervals:
            interval_data = road_data[road_data['时间间隔'] == interval]
            if len(interval_data) > 0:
                means.append(interval_data['通行时间_均值'].iloc[0])
            else:
                means.append(0)

        # 计算全局标准差
        global_std = np.std(means) if len(means) > 1 else 0

        # 自适应阈值：从严格到宽松逐步尝试
        thresholds = [0.1 * global_std, 0.05 * global_std, 0.02 * global_std, 0.01 * global_std]

        for threshold in thresholds:
            segments = []
            current_segment = [time_intervals[0]]
            current_mean = means[0]

            for i in range(1, len(time_intervals)):
                # 如果当前均值与时段均值差异超过阈值，开始新时段
                if abs(means[i] - current_mean) > threshold:
                    segments.append(current_segment)
                    current_segment = [time_intervals[i]]
                    current_mean = means[i]
                else:
                    current_segment.append(time_intervals[i])
                    # 更新时段均值
                    segment_means = [means[time_intervals.index(t)] for t in current_segment]
                    current_mean = np.mean(segment_means)

            segments.append(current_segment)

            # 如果达到目标时段数，返回结果
            if len(segments) >= target_segments:
                return segments

        # 如果所有阈值都无法达到目标，使用均匀划分
        return self._uniform_partition(time_intervals, target_segments)

    def _uniform_partition(self, time_intervals, target_segments):
        """均匀划分时间间隔"""
        if len(time_intervals) <= target_segments:
            return [[interval] for interval in time_intervals]

        segment_size = len(time_intervals) // target_segments
        segments = []

        for i in range(0, len(time_intervals), segment_size):
            segment = time_intervals[i:i + segment_size]
            if segment:
                segments.append(segment)

        # 如果有剩余的间隔，合并到最后一个时段
        if len(segments) > target_segments:
            last_segment = segments.pop()
            segments[-1].extend(last_segment)

        return segments

    def partition_by_clustering(self, road_data, max_clusters=8):
        """基于聚类的时段划分方法"""
        if len(road_data) < 2:
            return [road_data['时间间隔'].tolist()]
            
        # 准备特征数据
        features = road_data[['通行时间_均值', '通行时间_标准差', '速度_均值']].fillna(0)
        
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # 确定最优聚类数
        n_clusters = min(max_clusters, len(road_data))
        if n_clusters < 2:
            return [road_data['时间间隔'].tolist()]
            
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        road_data = road_data.copy()
        road_data['聚类标签'] = kmeans.fit_predict(features_scaled)
        
        # 按时间顺序重新排列聚类标签
        segments = []
        for cluster_id in sorted(road_data['聚类标签'].unique()):
            cluster_intervals = road_data[road_data['聚类标签'] == cluster_id]['时间间隔'].tolist()
            segments.append(sorted(cluster_intervals))
            
        return segments

class ImpedanceIntervalCalculator:
    """阻抗区间计算器"""
    
    def __init__(self, confidence_level=0.95):
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level
        
    def calculate_interval(self, segment_data, original_data):
        """计算时段阻抗区间"""
        if len(segment_data) == 0:
            return None
            
        # 获取原始数据用于计算区间
        segment_intervals = segment_data['时间间隔'].tolist()
        raw_travel_times = []
        
        for interval in segment_intervals:
            interval_data = original_data[original_data['时间间隔'] == interval]['通行时间(秒)']
            raw_travel_times.extend(interval_data.tolist())
            
        if len(raw_travel_times) < 2:
            return None
            
        travel_times = np.array(raw_travel_times)
        n = len(travel_times)
        
        # 基本统计量
        mean = np.mean(travel_times)
        std = np.std(travel_times, ddof=1)
        
        # 方法1：基于正态分布的置信区间
        t_critical = stats.t.ppf(1 - self.alpha/2, df=n-1)
        margin_error = t_critical * std / np.sqrt(n)
        
        normal_interval = {
            'lower': max(0, mean - margin_error),
            'upper': mean + margin_error,
            'method': 'normal_confidence'
        }
        
        # 方法2：基于分位数的区间
        percentile_interval = {
            'lower': np.percentile(travel_times, (self.alpha/2) * 100),
            'upper': np.percentile(travel_times, (1 - self.alpha/2) * 100),
            'method': 'percentile'
        }
        
        # 方法3：基于经验分布的区间
        empirical_interval = {
            'lower': np.min(travel_times),
            'upper': np.max(travel_times),
            'method': 'empirical'
        }
        
        return {
            'normal': normal_interval,
            'percentile': percentile_interval,
            'empirical': empirical_interval,
            'statistics': {
                'mean': mean,
                'std': std,
                'n': n,
                'cv': std / mean if mean > 0 else float('inf'),
                'median': np.median(travel_times)
            }
        }

class EmergencyTimeSegmentImpedanceSystem:
    """应急救援时段区间阻抗系统主类"""

    def __init__(self, confidence_level=0.95, emergency_mode=True):
        self.preprocessor = TrafficDataPreprocessor()
        self.partitioner = TimeSegmentPartitioner()
        self.calculator = ImpedanceIntervalCalculator(confidence_level)
        self.road_segments = {}
        self.original_data = None
        self.emergency_mode = emergency_mode
        self.emergency_factors = {}  # 应急因子
        self.real_time_updates = {}  # 实时更新数据

class TimeSegmentImpedanceSystem:
    """时段区间阻抗系统主类"""

    def __init__(self, confidence_level=0.95):
        self.preprocessor = TrafficDataPreprocessor()
        self.partitioner = TimeSegmentPartitioner()
        self.calculator = ImpedanceIntervalCalculator(confidence_level)
        self.road_segments = {}
        self.original_data = None

    def build_system(self, data_files, partition_method='statistical'):
        """构建完整的时段区间阻抗系统"""
        print("=== 开始构建时段区间阻抗系统 ===")

        # 1. 数据预处理
        print("\n1. 数据预处理...")
        self.preprocessor.load_data(data_files)
        self.preprocessor.clean_data()
        aggregated_data = self.preprocessor.aggregate_by_time_intervals(interval_minutes=10)

        # 保存原始清洗后的数据和时间间隔大小
        self.original_data = self.preprocessor.cleaned_data
        self.interval_minutes = 10  # 保存时间间隔大小

        print(f"聚合后数据: {len(aggregated_data)} 条记录")

        # 2. 为每条道路进行时段划分
        print("\n2. 时段划分...")
        road_ids = aggregated_data['路段ID'].unique()

        for i, road_id in enumerate(road_ids):
            print(f"处理路段 {road_id} ({i+1}/{len(road_ids)})")

            road_data = aggregated_data[aggregated_data['路段ID'] == road_id].copy()

            if len(road_data) < 2:
                print(f"  路段 {road_id} 数据不足，跳过")
                continue

            # 时段划分 - 目标更多时段
            if partition_method == 'statistical':
                segments = self.partitioner.partition_by_statistical_test(road_data, target_segments=7)
            else:
                segments = self.partitioner.partition_by_clustering(road_data, max_clusters=8)

            print(f"  划分为 {len(segments)} 个时段")

            # 计算每个时段的阻抗区间
            road_intervals = {}
            for j, segment_intervals in enumerate(segments):
                segment_data = road_data[road_data['时间间隔'].isin(segment_intervals)]

                if len(segment_data) == 0:
                    continue

                # 获取原始数据
                original_segment_data = self.original_data[
                    (self.original_data['路段ID'] == road_id) &
                    (self.original_data['时间间隔'].isin(segment_intervals))
                ]

                interval_result = self.calculator.calculate_interval(segment_data, original_segment_data)

                if interval_result:
                    start_time = min(segment_intervals) * self.interval_minutes  # 使用动态时间间隔
                    end_time = (max(segment_intervals) + 1) * self.interval_minutes

                    road_intervals[f'segment_{j}'] = {
                        'time_intervals': segment_intervals,
                        'impedance_intervals': interval_result,
                        'start_time': start_time,
                        'end_time': end_time,
                        'start_time_str': f"{start_time//60:02d}:{start_time%60:02d}",
                        'end_time_str': f"{end_time//60:02d}:{end_time%60:02d}"
                    }

            self.road_segments[road_id] = road_intervals

        print(f"\n成功处理 {len(self.road_segments)} 个路段")

    def get_impedance_interval(self, road_id, current_time_minutes, method='percentile'):
        """获取指定道路在指定时间的阻抗区间"""
        if road_id not in self.road_segments:
            return None

        road_data = self.road_segments[road_id]

        for segment_name, segment_info in road_data.items():
            if segment_info['start_time'] <= current_time_minutes < segment_info['end_time']:
                return segment_info['impedance_intervals'][method]

        return None

    def get_road_summary(self, road_id):
        """获取路段的时段划分摘要"""
        if road_id not in self.road_segments:
            return None

        road_data = self.road_segments[road_id]
        summary = {
            'road_id': road_id,
            'num_segments': len(road_data),
            'segments': []
        }

        for segment_name, segment_info in road_data.items():
            segment_summary = {
                'name': segment_name,
                'time_range': f"{segment_info['start_time_str']} - {segment_info['end_time_str']}",
                'mean_travel_time': segment_info['impedance_intervals']['statistics']['mean'],
                'std_travel_time': segment_info['impedance_intervals']['statistics']['std'],
                'percentile_interval': segment_info['impedance_intervals']['percentile']
            }
            summary['segments'].append(segment_summary)

        return summary

    def validate_system(self):
        """验证系统有效性"""
        print("\n=== 系统验证 ===")
        validation_results = {}

        for road_id, road_data in self.road_segments.items():
            if len(road_data) < 2:
                continue

            # 检查时段间差异性
            segment_means = []
            segment_stds = []

            for segment_info in road_data.values():
                stats = segment_info['impedance_intervals']['statistics']
                segment_means.append(stats['mean'])
                segment_stds.append(stats['std'])

            # 计算时段间方差
            inter_segment_variance = np.var(segment_means) if len(segment_means) > 1 else 0

            # 计算平均时段内方差
            avg_intra_segment_variance = np.mean([std**2 for std in segment_stds])

            # F统计量
            f_statistic = (inter_segment_variance / avg_intra_segment_variance
                          if avg_intra_segment_variance > 0 else float('inf'))

            # 变异系数
            cv_values = [segment_info['impedance_intervals']['statistics']['cv']
                        for segment_info in road_data.values()]
            avg_cv = np.mean(cv_values)

            validation_results[road_id] = {
                'f_statistic': f_statistic,
                'inter_segment_variance': inter_segment_variance,
                'avg_intra_segment_variance': avg_intra_segment_variance,
                'num_segments': len(road_data),
                'avg_cv': avg_cv,
                'segment_means': segment_means
            }

        return validation_results

    def visualize_road_segments(self, road_id, save_path=None):
        """可视化路段的时段划分结果"""
        if road_id not in self.road_segments:
            print(f"路段 {road_id} 不存在")
            return

        # 获取原始数据
        road_original_data = self.original_data[self.original_data['路段ID'] == road_id].copy()

        if len(road_original_data) == 0:
            print(f"路段 {road_id} 没有数据")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'路段 {road_id} 时段区间阻抗分析', fontsize=16, fontweight='bold')

        # 1. 通行时间时序图
        ax1 = axes[0, 0]
        road_original_data_sorted = road_original_data.sort_values('时间分钟')
        ax1.scatter(road_original_data_sorted['时间分钟'], road_original_data_sorted['通行时间(秒)'],
                   alpha=0.6, s=20)

        # 添加时段分割线和区间
        road_data = self.road_segments[road_id]
        colors = plt.cm.Set3(np.linspace(0, 1, len(road_data)))

        for i, (segment_name, segment_info) in enumerate(road_data.items()):
            start_time = segment_info['start_time']
            end_time = segment_info['end_time']
            interval_data = segment_info['impedance_intervals']['percentile']

            # 绘制时段区间
            ax1.axvspan(start_time, end_time, alpha=0.2, color=colors[i],
                       label=f"{segment_info['start_time_str']}-{segment_info['end_time_str']}")

            # 绘制阻抗区间
            ax1.axhline(y=interval_data['lower'], xmin=(start_time-420)/180, xmax=(end_time-420)/180,
                       color=colors[i], linestyle='--', alpha=0.8)
            ax1.axhline(y=interval_data['upper'], xmin=(start_time-420)/180, xmax=(end_time-420)/180,
                       color=colors[i], linestyle='--', alpha=0.8)

        ax1.set_xlabel('时间 (分钟)')
        ax1.set_ylabel('通行时间 (秒)')
        ax1.set_title('通行时间时序分布')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 2. 各时段阻抗区间对比
        ax2 = axes[0, 1]
        segment_names = []
        lower_bounds = []
        upper_bounds = []
        means = []

        for segment_name, segment_info in road_data.items():
            segment_names.append(f"{segment_info['start_time_str']}-{segment_info['end_time_str']}")
            interval_data = segment_info['impedance_intervals']['percentile']
            stats_data = segment_info['impedance_intervals']['statistics']

            lower_bounds.append(interval_data['lower'])
            upper_bounds.append(interval_data['upper'])
            means.append(stats_data['mean'])

        x_pos = np.arange(len(segment_names))

        # 绘制区间
        ax2.errorbar(x_pos, means,
                    yerr=[np.array(means) - np.array(lower_bounds),
                          np.array(upper_bounds) - np.array(means)],
                    fmt='o', capsize=5, capthick=2, markersize=8)

        ax2.set_xlabel('时段')
        ax2.set_ylabel('通行时间 (秒)')
        ax2.set_title('各时段阻抗区间对比')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(segment_names, rotation=45)
        ax2.grid(True, alpha=0.3)

        # 3. 时段内通行时间分布
        ax3 = axes[1, 0]

        for i, (segment_name, segment_info) in enumerate(road_data.items()):
            segment_intervals = segment_info['time_intervals']
            segment_data = road_original_data[road_original_data['时间间隔'].isin(segment_intervals)]

            if len(segment_data) > 0:
                ax3.hist(segment_data['通行时间(秒)'], bins=15, alpha=0.6,
                        color=colors[i], label=f"{segment_info['start_time_str']}-{segment_info['end_time_str']}")

        ax3.set_xlabel('通行时间 (秒)')
        ax3.set_ylabel('频次')
        ax3.set_title('各时段通行时间分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 统计指标对比
        ax4 = axes[1, 1]

        metrics = ['均值', '标准差', '变异系数']
        segment_metrics = {metric: [] for metric in metrics}

        for segment_info in road_data.values():
            stats_data = segment_info['impedance_intervals']['statistics']
            segment_metrics['均值'].append(stats_data['mean'])
            segment_metrics['标准差'].append(stats_data['std'])
            segment_metrics['变异系数'].append(stats_data['cv'])

        x = np.arange(len(segment_names))
        width = 0.25

        for i, metric in enumerate(metrics):
            if metric == '变异系数':
                # 变异系数使用右侧y轴
                ax4_twin = ax4.twinx()
                ax4_twin.bar(x + i*width, segment_metrics[metric], width,
                           label=metric, alpha=0.7, color='red')
                ax4_twin.set_ylabel('变异系数', color='red')
                ax4_twin.tick_params(axis='y', labelcolor='red')
            else:
                ax4.bar(x + i*width, segment_metrics[metric], width,
                       label=metric, alpha=0.7)

        ax4.set_xlabel('时段')
        ax4.set_ylabel('通行时间 (秒)')
        ax4.set_title('各时段统计指标对比')
        ax4.set_xticks(x + width)
        ax4.set_xticklabels(segment_names, rotation=45)
        ax4.legend(loc='upper left')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")

        plt.show()

    def generate_report(self, output_file='时段划分程序/系统1运行结果.txt'):
        """生成分析报告"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("时段区间阻抗系统分析报告\n")
            f.write("=" * 60 + "\n\n")

            # 系统概览
            f.write("1. 系统概览\n")
            f.write("-" * 30 + "\n")
            f.write(f"处理路段数量: {len(self.road_segments)}\n")
            f.write(f"原始数据记录数: {len(self.original_data)}\n")
            f.write(f"数据时间范围: {self.original_data['采集时间'].min()} 到 {self.original_data['采集时间'].max()}\n\n")

            # 验证结果
            validation_results = self.validate_system()
            f.write("2. 系统验证结果\n")
            f.write("-" * 30 + "\n")

            valid_roads = 0
            total_segments = 0

            for road_id, result in validation_results.items():
                if result['f_statistic'] > 1.0:  # F统计量大于1表示时段间差异明显
                    valid_roads += 1
                total_segments += result['num_segments']

            f.write(f"有效路段数量: {valid_roads}/{len(validation_results)}\n")
            f.write(f"平均时段数量: {total_segments/len(validation_results):.1f}\n")
            f.write(f"平均F统计量: {np.mean([r['f_statistic'] for r in validation_results.values() if not np.isinf(r['f_statistic'])]):.2f}\n\n")

            # 详细路段信息
            f.write("3. 路段详细信息\n")
            f.write("-" * 30 + "\n")

            for road_id in sorted(self.road_segments.keys()):
                summary = self.get_road_summary(road_id)
                if summary:
                    f.write(f"\n路段 {road_id}:\n")
                    f.write(f"  时段数量: {summary['num_segments']}\n")

                    for segment in summary['segments']:
                        f.write(f"  {segment['time_range']}: ")
                        f.write(f"均值={segment['mean_travel_time']:.1f}秒, ")
                        f.write(f"区间=[{segment['percentile_interval']['lower']:.1f}, {segment['percentile_interval']['upper']:.1f}]秒\n")

        print(f"分析报告已生成: {output_file}")

    def compare_with_traditional_method(self, road_id):
        """与传统区间阻抗方法对比"""
        if road_id not in self.road_segments:
            print(f"路段 {road_id} 不存在")
            return None

        # 获取原始数据
        road_data = self.original_data[self.original_data['路段ID'] == road_id]['通行时间(秒)']

        if len(road_data) == 0:
            return None

        # 传统方法：整体区间
        traditional_interval = {
            'lower': road_data.quantile(0.025),
            'upper': road_data.quantile(0.975),
            'width': road_data.quantile(0.975) - road_data.quantile(0.025)
        }

        # 时段区间方法：各时段区间
        segment_intervals = []
        for segment_info in self.road_segments[road_id].values():
            interval_data = segment_info['impedance_intervals']['percentile']
            segment_intervals.append({
                'lower': interval_data['lower'],
                'upper': interval_data['upper'],
                'width': interval_data['upper'] - interval_data['lower'],
                'time_range': f"{segment_info['start_time_str']}-{segment_info['end_time_str']}"
            })

        # 计算改进指标
        avg_segment_width = np.mean([seg['width'] for seg in segment_intervals])
        width_reduction = (traditional_interval['width'] - avg_segment_width) / traditional_interval['width'] * 100

        comparison = {
            'road_id': road_id,
            'traditional': traditional_interval,
            'segments': segment_intervals,
            'improvement': {
                'width_reduction_percent': width_reduction,
                'avg_segment_width': avg_segment_width,
                'num_segments': len(segment_intervals)
            }
        }

        return comparison

def main():
    """主函数 - 演示时段区间阻抗系统的使用"""
    print("时段区间阻抗系统演示")
    print("=" * 50)

    # 数据文件路径
    data_files = [
        '采集数据/早高峰/augment7.23早高峰.csv',
        '采集数据/早高峰/augment7.24早高峰.csv',
        '采集数据/早高峰/augment7.25早高峰.csv',
        '采集数据/早高峰/augment7.26早高峰.csv',
        '采集数据/早高峰/augment7.27早高峰.csv',
        '采集数据/早高峰/augment7.28早高峰.csv',
        '采集数据/早高峰/augment7.29早高峰.csv',
        '采集数据/早高峰/augment7.30早高峰.csv',
        '采集数据/早高峰/augment7.31早高峰.csv',
        '采集数据/早高峰/augment8.1早高峰.csv',
        '采集数据/早高峰/augment8.2早高峰.csv',
        '采集数据/早高峰/augment8.3早高峰.csv'
    ]

    # 检查文件是否存在
    import os
    existing_files = [f for f in data_files if os.path.exists(f)]

    if not existing_files:
        print("错误：找不到数据文件")
        print("请确保以下文件存在：")
        for f in data_files:
            print(f"  - {f}")
        return

    print(f"找到 {len(existing_files)} 个数据文件")

    # 创建系统实例
    system = TimeSegmentImpedanceSystem(confidence_level=0.95)

    try:
        # 构建系统
        system.build_system(existing_files, partition_method='statistical')

        # 生成验证报告
        validation_results = system.validate_system()

        # 选择几个代表性路段进行详细分析
        sample_roads = list(system.road_segments.keys())[:5]  # 取前5个路段

        print(f"\n=== 详细分析示例路段 ===")
        for road_id in sample_roads:
            print(f"\n路段 {road_id}:")

            # 获取路段摘要
            summary = system.get_road_summary(road_id)
            if summary:
                print(f"  时段数量: {summary['num_segments']}")
                for segment in summary['segments']:
                    print(f"    {segment['time_range']}: 均值={segment['mean_travel_time']:.1f}秒, "
                          f"区间=[{segment['percentile_interval']['lower']:.1f}, "
                          f"{segment['percentile_interval']['upper']:.1f}]秒")

            # 与传统方法对比
            comparison = system.compare_with_traditional_method(road_id)
            if comparison:
                print(f"  传统方法区间宽度: {comparison['traditional']['width']:.1f}秒")
                print(f"  时段方法平均宽度: {comparison['improvement']['avg_segment_width']:.1f}秒")
                print(f"  区间宽度减少: {comparison['improvement']['width_reduction_percent']:.1f}%")

        # 生成分析报告
        system.generate_report()

        # 可视化示例（选择第一个路段）
        if sample_roads:
            print(f"\n正在生成路段 {sample_roads[0]} 的可视化图表...")
            system.visualize_road_segments(sample_roads[0], f'路段_{sample_roads[0]}_分析图.png')

        # 演示实时查询功能
        print(f"\n=== 实时查询演示 ===")
        test_times = [7*60+30, 8*60, 8*60+30]  # 7:30, 8:00, 8:30

        for road_id in sample_roads[:3]:
            print(f"\n路段 {road_id}:")
            for time_minutes in test_times:
                hour = time_minutes // 60
                minute = time_minutes % 60
                interval = system.get_impedance_interval(road_id, time_minutes)

                if interval:
                    print(f"  {hour:02d}:{minute:02d} - 阻抗区间: [{interval['lower']:.1f}, {interval['upper']:.1f}]秒")
                else:
                    print(f"  {hour:02d}:{minute:02d} - 无数据")

        print(f"\n=== 系统性能总结 ===")
        total_roads = len(system.road_segments)
        total_segments = sum(len(segments) for segments in system.road_segments.values())

        print(f"处理路段数量: {total_roads}")
        print(f"总时段数量: {total_segments}")
        print(f"平均每路段时段数: {total_segments/total_roads:.1f}")

        # 计算整体改进效果
        improvements = []
        for road_id in system.road_segments.keys():
            comparison = system.compare_with_traditional_method(road_id)
            if comparison and comparison['improvement']['width_reduction_percent'] > 0:
                improvements.append(comparison['improvement']['width_reduction_percent'])

        if improvements:
            print(f"平均区间宽度减少: {np.mean(improvements):.1f}%")
            print(f"最大区间宽度减少: {np.max(improvements):.1f}%")

        print("\n系统构建完成！")

    except Exception as e:
        print(f"系统构建失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
