============================================================
自适应时段区间阻抗系统分析报告
============================================================

1. 系统概览
------------------------------
处理路段数量: 67
原始数据记录数: 28895
数据时间范围: 2025-07-28 07:00:03 到 2025-08-04 09:00:00

2. 自适应划分统计
------------------------------
时段数量分布:
  5个时段: 0个路段 (0.0%)
  6个时段: 1个路段 (1.5%)
  7个时段: 63个路段 (94.0%)
  8个时段: 3个路段 (4.5%)
平均时段数量: 7.0
时段数量标准差: 0.2

3. 划分方法统计
------------------------------
  change_rate: 56个路段 (83.6%)
  adaptive_threshold: 11个路段 (16.4%)

4. 系统验证结果
------------------------------
有效路段数量: 1/67
平均时段数量: 7.0
平均F统计量: 0.18
平均质量评分: 167.70

5. 路段详细信息
------------------------------

路段 1-2:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 185.91
  07:00 - 07:30: 均值=200.3秒, 区间=[157.0, 238.4]秒
  07:30 - 07:50: 均值=207.9秒, 区间=[168.5, 259.0]秒
  07:50 - 08:00: 均值=221.5秒, 区间=[181.0, 282.5]秒
  08:00 - 08:10: 均值=209.1秒, 区间=[175.8, 272.2]秒
  08:10 - 08:40: 均值=218.2秒, 区间=[167.0, 265.7]秒
  08:40 - 08:50: 均值=224.2秒, 区间=[164.9, 255.1]秒
  08:50 - 09:00: 均值=235.3秒, 区间=[192.9, 276.0]秒

路段 1-9:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 79.35
  07:00 - 07:10: 均值=211.3秒, 区间=[175.8, 240.1]秒
  07:10 - 07:20: 均值=224.2秒, 区间=[197.9, 246.5]秒
  07:20 - 07:30: 均值=235.8秒, 区间=[196.9, 261.6]秒
  07:30 - 07:40: 均值=241.2秒, 区间=[196.9, 295.6]秒
  07:40 - 08:00: 均值=251.4秒, 区间=[210.0, 310.8]秒
  08:00 - 08:10: 均值=259.5秒, 区间=[204.9, 312.8]秒
  08:10 - 09:00: 均值=247.4秒, 区间=[208.7, 308.7]秒

路段 10-11:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 17.68
  07:00 - 07:20: 均值=183.9秒, 区间=[146.8, 225.7]秒
  07:20 - 08:00: 均值=198.3秒, 区间=[152.3, 241.3]秒
  08:00 - 08:20: 均值=197.5秒, 区间=[160.4, 245.0]秒
  08:20 - 08:30: 均值=192.0秒, 区间=[162.9, 240.0]秒
  08:30 - 08:40: 均值=199.3秒, 区间=[160.8, 246.2]秒
  08:40 - 08:50: 均值=186.3秒, 区间=[159.0, 221.6]秒
  08:50 - 09:00: 均值=201.9秒, 区间=[165.9, 250.5]秒

路段 10-18:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 56.48
  07:00 - 07:10: 均值=82.8秒, 区间=[73.0, 93.4]秒
  07:10 - 07:40: 均值=85.5秒, 区间=[72.1, 100.9]秒
  07:40 - 07:50: 均值=86.3秒, 区间=[74.4, 102.0]秒
  07:50 - 08:00: 均值=81.4秒, 区间=[71.9, 98.1]秒
  08:00 - 08:20: 均值=84.3秒, 区间=[75.8, 98.0]秒
  08:20 - 08:50: 均值=85.7秒, 区间=[78.0, 98.1]秒
  08:50 - 09:00: 均值=83.6秒, 区间=[77.9, 97.1]秒

路段 11-12:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 40.88
  07:00 - 07:10: 均值=248.1秒, 区间=[219.9, 289.1]秒
  07:10 - 07:20: 均值=241.2秒, 区间=[209.9, 287.1]秒
  07:20 - 07:30: 均值=251.5秒, 区间=[215.2, 284.0]秒
  07:30 - 07:40: 均值=261.4秒, 区间=[226.9, 316.5]秒
  07:40 - 08:10: 均值=270.6秒, 区间=[233.6, 293.5]秒
  08:10 - 08:50: 均值=260.1秒, 区间=[229.7, 296.8]秒
  08:50 - 09:00: 均值=248.7秒, 区间=[223.7, 291.8]秒

路段 11-19:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 73.97
  07:00 - 07:20: 均值=213.0秒, 区间=[179.0, 249.0]秒
  07:20 - 07:40: 均值=221.0秒, 区间=[186.1, 251.2]秒
  07:40 - 07:50: 均值=229.8秒, 区间=[198.9, 256.2]秒
  07:50 - 08:00: 均值=242.2秒, 区间=[207.0, 272.1]秒
  08:00 - 08:10: 均值=230.0秒, 区间=[207.8, 262.1]秒
  08:10 - 08:20: 均值=226.1秒, 区间=[190.4, 262.7]秒
  08:20 - 09:00: 均值=221.4秒, 区间=[185.0, 265.6]秒

路段 12-13:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 26.40
  07:00 - 07:10: 均值=170.0秒, 区间=[138.9, 205.3]秒
  07:10 - 07:20: 均值=179.6秒, 区间=[145.6, 206.3]秒
  07:20 - 07:50: 均值=172.6秒, 区间=[143.7, 205.7]秒
  07:50 - 08:00: 均值=168.8秒, 区间=[137.8, 204.4]秒
  08:00 - 08:30: 均值=172.3秒, 区间=[137.7, 212.0]秒
  08:30 - 08:40: 均值=166.1秒, 区间=[147.9, 188.1]秒
  08:40 - 09:00: 均值=170.2秒, 区间=[140.7, 208.7]秒

路段 12-20:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 67.77
  07:00 - 07:10: 均值=172.4秒, 区间=[159.4, 193.2]秒
  07:10 - 07:30: 均值=182.7秒, 区间=[161.0, 195.9]秒
  07:30 - 07:50: 均值=175.2秒, 区间=[161.8, 191.6]秒
  07:50 - 08:00: 均值=188.7秒, 区间=[175.9, 203.2]秒
  08:00 - 08:10: 均值=184.3秒, 区间=[161.1, 197.7]秒
  08:10 - 08:50: 均值=177.6秒, 区间=[160.2, 200.3]秒
  08:50 - 09:00: 均值=186.6秒, 区间=[168.0, 197.1]秒

路段 13-14:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 36.73
  07:00 - 07:30: 均值=101.9秒, 区间=[75.3, 135.2]秒
  07:30 - 07:40: 均值=105.2秒, 区间=[80.9, 134.1]秒
  07:40 - 08:00: 均值=101.2秒, 区间=[76.9, 129.1]秒
  08:00 - 08:10: 均值=104.0秒, 区间=[78.7, 134.2]秒
  08:10 - 08:30: 均值=106.9秒, 区间=[88.4, 135.0]秒
  08:30 - 08:50: 均值=109.5秒, 区间=[90.5, 140.0]秒
  08:50 - 09:00: 均值=107.2秒, 区间=[89.9, 132.4]秒

路段 13-21:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 30.69
  07:00 - 07:10: 均值=69.7秒, 区间=[52.0, 96.1]秒
  07:10 - 07:20: 均值=75.5秒, 区间=[58.8, 95.3]秒
  07:20 - 07:40: 均值=67.7秒, 区间=[50.6, 95.0]秒
  07:40 - 08:00: 均值=72.0秒, 区间=[54.0, 91.0]秒
  08:00 - 08:10: 均值=63.8秒, 区间=[51.0, 90.2]秒
  08:10 - 08:50: 均值=68.5秒, 区间=[53.0, 91.4]秒
  08:50 - 09:00: 均值=70.8秒, 区间=[50.9, 94.2]秒

路段 14-15:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 190.63
  07:00 - 07:20: 均值=213.9秒, 区间=[169.3, 264.0]秒
  07:20 - 07:30: 均值=222.2秒, 区间=[181.5, 275.3]秒
  07:30 - 07:40: 均值=224.1秒, 区间=[182.7, 272.1]秒
  07:40 - 07:50: 均值=213.9秒, 区间=[182.8, 249.1]秒
  07:50 - 08:10: 均值=225.1秒, 区间=[191.6, 273.2]秒
  08:10 - 08:30: 均值=229.7秒, 区间=[191.6, 273.3]秒
  08:30 - 09:00: 均值=224.5秒, 区间=[191.0, 270.3]秒

路段 14-22:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 19.39
  07:00 - 07:40: 均值=142.2秒, 区间=[108.7, 179.5]秒
  07:40 - 08:00: 均值=148.8秒, 区间=[106.1, 183.1]秒
  08:00 - 08:10: 均值=134.5秒, 区间=[104.8, 180.6]秒
  08:10 - 08:20: 均值=137.9秒, 区间=[102.5, 178.7]秒
  08:20 - 08:40: 均值=140.8秒, 区间=[110.0, 170.4]秒
  08:40 - 08:50: 均值=134.3秒, 区间=[107.7, 161.2]秒
  08:50 - 09:00: 均值=142.8秒, 区间=[108.6, 190.1]秒

路段 15-16:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 66.19
  07:00 - 07:10: 均值=66.7秒, 区间=[46.9, 91.3]秒
  07:10 - 07:20: 均值=68.3秒, 区间=[48.0, 91.2]秒
  07:20 - 07:30: 均值=72.9秒, 区间=[49.9, 98.4]秒
  07:30 - 08:10: 均值=75.9秒, 区间=[55.0, 96.6]秒
  08:10 - 08:20: 均值=77.1秒, 区间=[53.0, 95.0]秒
  08:20 - 08:30: 均值=72.1秒, 区间=[52.0, 90.0]秒
  08:30 - 09:00: 均值=74.8秒, 区间=[52.8, 96.2]秒

路段 15-23:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 41.33
  07:00 - 07:20: 均值=51.9秒, 区间=[35.5, 72.0]秒
  07:20 - 08:00: 均值=57.5秒, 区间=[35.0, 84.2]秒
  08:00 - 08:10: 均值=53.4秒, 区间=[43.0, 80.8]秒
  08:10 - 08:30: 均值=58.3秒, 区间=[40.9, 80.0]秒
  08:30 - 08:40: 均值=55.5秒, 区间=[38.0, 86.2]秒
  08:40 - 08:50: 均值=51.7秒, 区间=[35.8, 75.0]秒
  08:50 - 09:00: 均值=54.0秒, 区间=[34.7, 82.0]秒

路段 16-24:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 26.04
  07:00 - 07:10: 均值=192.8秒, 区间=[168.9, 219.2]秒
  07:10 - 07:20: 均值=196.8秒, 区间=[169.0, 219.7]秒
  07:20 - 07:30: 均值=205.2秒, 区间=[169.8, 237.1]秒
  07:30 - 07:40: 均值=206.1秒, 区间=[180.9, 229.1]秒
  07:40 - 07:50: 均值=203.0秒, 区间=[179.9, 222.2]秒
  07:50 - 08:00: 均值=200.9秒, 区间=[181.3, 227.5]秒
  08:00 - 09:00: 均值=197.2秒, 区间=[172.5, 228.0]秒

路段 17-18:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 14.56
  07:00 - 07:20: 均值=88.3秒, 区间=[70.1, 116.4]秒
  07:20 - 07:40: 均值=87.1秒, 区间=[68.6, 115.1]秒
  07:40 - 07:50: 均值=89.6秒, 区间=[72.3, 109.3]秒
  07:50 - 08:10: 均值=86.8秒, 区间=[70.6, 115.4]秒
  08:10 - 08:20: 均值=90.8秒, 区间=[78.0, 115.0]秒
  08:20 - 08:30: 均值=86.6秒, 区间=[69.4, 107.6]秒
  08:30 - 09:00: 均值=91.6秒, 区间=[80.0, 107.0]秒

路段 17-25:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 16.14
  07:00 - 07:10: 均值=238.5秒, 区间=[206.0, 274.6]秒
  07:10 - 07:20: 均值=236.1秒, 区间=[209.6, 256.2]秒
  07:20 - 07:30: 均值=227.9秒, 区间=[207.9, 264.7]秒
  07:30 - 07:40: 均值=237.8秒, 区间=[208.5, 269.9]秒
  07:40 - 08:00: 均值=242.9秒, 区间=[217.0, 261.9]秒
  08:00 - 08:10: 均值=232.2秒, 区间=[211.0, 249.2]秒
  08:10 - 09:00: 均值=238.4秒, 区间=[217.1, 257.8]秒

路段 18-19:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 25.09
  07:00 - 07:10: 均值=74.4秒, 区间=[54.7, 103.6]秒
  07:10 - 07:20: 均值=77.0秒, 区间=[60.8, 98.7]秒
  07:20 - 07:30: 均值=80.5秒, 区间=[59.8, 103.1]秒
  07:30 - 07:40: 均值=77.8秒, 区间=[63.0, 100.1]秒
  07:40 - 07:50: 均值=81.9秒, 区间=[64.0, 106.0]秒
  07:50 - 08:00: 均值=84.6秒, 区间=[64.6, 108.4]秒
  08:00 - 09:00: 均值=81.2秒, 区间=[62.0, 109.0]秒

路段 18-26:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 195.35
  07:00 - 07:10: 均值=90.5秒, 区间=[80.0, 112.0]秒
  07:10 - 07:20: 均值=94.2秒, 区间=[86.0, 112.7]秒
  07:20 - 07:30: 均值=92.0秒, 区间=[79.0, 114.0]秒
  07:30 - 08:10: 均值=95.3秒, 区间=[89.3, 104.7]秒
  08:10 - 08:40: 均值=98.2秒, 区间=[92.0, 109.3]秒
  08:40 - 08:50: 均值=100.2秒, 区间=[94.0, 110.2]秒
  08:50 - 09:00: 均值=98.3秒, 区间=[92.8, 104.4]秒

路段 19-20:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 72.94
  07:00 - 07:30: 均值=117.4秒, 区间=[92.0, 145.6]秒
  07:30 - 07:40: 均值=125.0秒, 区间=[102.0, 145.1]秒
  07:40 - 08:10: 均值=128.4秒, 区间=[105.0, 153.0]秒
  08:10 - 08:20: 均值=131.0秒, 区间=[101.9, 159.1]秒
  08:20 - 08:40: 均值=126.4秒, 区间=[93.0, 155.0]秒
  08:40 - 08:50: 均值=128.1秒, 区间=[108.7, 158.4]秒
  08:50 - 09:00: 均值=120.6秒, 区间=[106.7, 150.2]秒

路段 19-27:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 315.15
  07:00 - 07:10: 均值=69.6秒, 区间=[53.0, 98.4]秒
  07:10 - 07:20: 均值=73.8秒, 区间=[55.6, 99.5]秒
  07:20 - 07:30: 均值=78.3秒, 区间=[56.0, 98.5]秒
  07:30 - 08:30: 均值=82.3秒, 区间=[63.0, 106.0]秒
  08:30 - 08:40: 均值=72.8秒, 区间=[56.9, 94.2]秒
  08:40 - 08:50: 均值=80.8秒, 区间=[60.0, 104.6]秒
  08:50 - 09:00: 均值=77.5秒, 区间=[60.0, 97.1]秒

路段 2-10:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 43.10
  07:00 - 07:10: 均值=74.7秒, 区间=[61.8, 92.2]秒
  07:10 - 07:20: 均值=76.6秒, 区间=[59.9, 93.0]秒
  07:20 - 07:40: 均值=80.1秒, 区间=[66.0, 98.0]秒
  07:40 - 08:00: 均值=87.3秒, 区间=[64.2, 101.0]秒
  08:00 - 08:10: 均值=82.2秒, 区间=[69.0, 95.0]秒
  08:10 - 08:40: 均值=76.5秒, 区间=[69.0, 88.7]秒
  08:40 - 09:00: 均值=79.6秒, 区间=[71.0, 98.1]秒

路段 2-3:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 2044.91
  07:00 - 07:30: 均值=222.2秒, 区间=[192.7, 268.4]秒
  07:30 - 07:50: 均值=234.7秒, 区间=[200.7, 276.3]秒
  07:50 - 08:00: 均值=240.4秒, 区间=[206.9, 270.4]秒
  08:00 - 08:10: 均值=252.1秒, 区间=[211.8, 290.1]秒
  08:10 - 08:20: 均值=248.4秒, 区间=[223.0, 290.1]秒
  08:20 - 08:30: 均值=251.9秒, 区间=[228.8, 275.7]秒
  08:30 - 09:00: 均值=243.9秒, 区间=[217.9, 269.0]秒

路段 20-21:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 50.79
  07:00 - 07:10: 均值=95.3秒, 区间=[74.0, 116.0]秒
  07:10 - 07:20: 均值=91.0秒, 区间=[76.8, 110.0]秒
  07:20 - 07:40: 均值=95.9秒, 区间=[78.9, 125.0]秒
  07:40 - 08:00: 均值=103.2秒, 区间=[83.8, 129.5]秒
  08:00 - 08:20: 均值=111.3秒, 区间=[89.4, 133.2]秒
  08:20 - 08:50: 均值=104.0秒, 区间=[88.0, 125.2]秒
  08:50 - 09:00: 均值=99.1秒, 区间=[85.8, 117.0]秒

路段 20-28:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 76.05
  07:00 - 07:10: 均值=129.8秒, 区间=[118.9, 141.1]秒
  07:10 - 07:40: 均值=134.5秒, 区间=[121.0, 155.8]秒
  07:40 - 08:00: 均值=137.4秒, 区间=[124.7, 157.0]秒
  08:00 - 08:10: 均值=142.6秒, 区间=[128.8, 157.0]秒
  08:10 - 08:20: 均值=140.1秒, 区间=[128.0, 155.1]秒
  08:20 - 08:50: 均值=137.9秒, 区间=[128.0, 157.3]秒
  08:50 - 09:00: 均值=139.3秒, 区间=[128.0, 158.3]秒

路段 21-22:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 0.00
  07:00 - 07:10: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:10 - 07:20: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:20 - 07:30: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:30 - 07:40: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:40 - 07:50: 均值=1.0秒, 区间=[1.0, 1.0]秒
  07:50 - 08:00: 均值=1.0秒, 区间=[1.0, 1.0]秒
  08:00 - 09:00: 均值=1.0秒, 区间=[1.0, 1.0]秒

路段 21-29:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 47.66
  07:00 - 07:20: 均值=191.2秒, 区间=[158.6, 227.9]秒
  07:20 - 07:30: 均值=193.9秒, 区间=[159.7, 231.1]秒
  07:30 - 07:50: 均值=198.5秒, 区间=[167.4, 235.0]秒
  07:50 - 08:30: 均值=208.7秒, 区间=[175.4, 249.0]秒
  08:30 - 08:40: 均值=196.5秒, 区间=[163.9, 232.1]秒
  08:40 - 08:50: 均值=205.6秒, 区间=[165.8, 255.1]秒
  08:50 - 09:00: 均值=198.7秒, 区间=[170.8, 237.3]秒

路段 22-23:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 93.91
  07:00 - 07:40: 均值=267.4秒, 区间=[216.2, 347.0]秒
  07:40 - 07:50: 均值=287.1秒, 区间=[243.9, 347.0]秒
  07:50 - 08:00: 均值=280.2秒, 区间=[219.2, 343.4]秒
  08:00 - 08:30: 均值=290.0秒, 区间=[241.6, 346.0]秒
  08:30 - 08:40: 均值=263.2秒, 区间=[223.4, 299.8]秒
  08:40 - 08:50: 均值=284.0秒, 区间=[237.8, 342.2]秒
  08:50 - 09:00: 均值=275.0秒, 区间=[234.9, 332.7]秒

路段 22-30:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 53.56
  07:00 - 07:20: 均值=244.3秒, 区间=[201.8, 297.8]秒
  07:20 - 08:00: 均值=252.8秒, 区间=[207.7, 300.3]秒
  08:00 - 08:20: 均值=266.5秒, 区间=[221.0, 324.1]秒
  08:20 - 08:30: 均值=273.4秒, 区间=[231.5, 326.2]秒
  08:30 - 08:40: 均值=243.5秒, 区间=[206.0, 274.9]秒
  08:40 - 08:50: 均值=256.2秒, 区间=[215.7, 294.3]秒
  08:50 - 09:00: 均值=243.6秒, 区间=[209.2, 298.4]秒

路段 23-24:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 28.46
  07:00 - 07:20: 均值=169.3秒, 区间=[144.5, 201.2]秒
  07:20 - 07:30: 均值=178.0秒, 区间=[133.8, 211.1]秒
  07:30 - 08:00: 均值=181.3秒, 区间=[149.9, 209.7]秒
  08:00 - 08:10: 均值=169.4秒, 区间=[138.8, 197.3]秒
  08:10 - 08:30: 均值=171.6秒, 区间=[149.0, 194.4]秒
  08:30 - 08:50: 均值=178.1秒, 区间=[143.0, 210.0]秒
  08:50 - 09:00: 均值=173.1秒, 区间=[149.9, 201.4]秒

路段 23-31:
  时段数量: 8 (目标: 8)
  划分方法: adaptive_threshold
  质量评分: 872.58
  07:00 - 07:10: 均值=64.4秒, 区间=[37.9, 112.1]秒
  07:10 - 07:20: 均值=59.9秒, 区间=[30.9, 97.1]秒
  07:20 - 07:30: 均值=69.1秒, 区间=[43.6, 103.6]秒
  07:30 - 07:40: 均值=67.6秒, 区间=[34.6, 117.0]秒
  07:40 - 07:50: 均值=73.6秒, 区间=[45.0, 104.0]秒
  07:50 - 08:00: 均值=80.9秒, 区间=[38.6, 119.2]秒
  08:00 - 08:20: 均值=79.5秒, 区间=[46.9, 110.4]秒
  08:20 - 09:00: 均值=73.8秒, 区间=[47.7, 103.3]秒

路段 24-32:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 155.68
  07:00 - 07:10: 均值=69.8秒, 区间=[63.0, 79.1]秒
  07:10 - 07:20: 均值=76.2秒, 区间=[61.6, 94.9]秒
  07:20 - 07:40: 均值=82.3秒, 区间=[61.0, 114.4]秒
  07:40 - 08:00: 均值=90.3秒, 区间=[61.0, 120.0]秒
  08:00 - 08:10: 均值=86.0秒, 区间=[61.6, 113.9]秒
  08:10 - 08:30: 均值=82.1秒, 区间=[59.0, 102.5]秒
  08:30 - 09:00: 均值=78.9秒, 区间=[63.3, 92.3]秒

路段 25-26:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 64.52
  07:00 - 07:10: 均值=71.5秒, 区间=[64.6, 87.0]秒
  07:10 - 07:40: 均值=74.3秒, 区间=[66.0, 82.8]秒
  07:40 - 07:50: 均值=76.3秒, 区间=[72.0, 81.2]秒
  07:50 - 08:30: 均值=74.0秒, 区间=[68.0, 82.0]秒
  08:30 - 08:40: 均值=76.4秒, 区间=[71.6, 84.0]秒
  08:40 - 08:50: 均值=74.5秒, 区间=[71.0, 79.3]秒
  08:50 - 09:00: 均值=76.3秒, 区间=[72.0, 85.4]秒

路段 25-33:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 93.56
  07:00 - 07:20: 均值=294.0秒, 区间=[239.4, 378.5]秒
  07:20 - 07:40: 均值=300.5秒, 区间=[265.9, 375.7]秒
  07:40 - 07:50: 均值=324.2秒, 区间=[278.9, 398.1]秒
  07:50 - 08:00: 均值=311.2秒, 区间=[267.0, 365.9]秒
  08:00 - 08:10: 均值=287.0秒, 区间=[258.4, 356.1]秒
  08:10 - 08:30: 均值=294.3秒, 区间=[238.8, 387.6]秒
  08:30 - 09:00: 均值=283.8秒, 区间=[252.3, 366.6]秒

路段 26-27:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 45.99
  07:00 - 07:10: 均值=118.8秒, 区间=[106.0, 131.1]秒
  07:10 - 07:40: 均值=124.0秒, 区间=[106.6, 141.0]秒
  07:40 - 08:00: 均值=127.3秒, 区间=[109.5, 143.4]秒
  08:00 - 08:20: 均值=128.9秒, 区间=[107.1, 143.2]秒
  08:20 - 08:30: 均值=123.8秒, 区间=[112.5, 138.2]秒
  08:30 - 08:40: 均值=120.1秒, 区间=[112.0, 138.0]秒
  08:40 - 09:00: 均值=115.9秒, 区间=[108.7, 133.7]秒

路段 26-34:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 39.97
  07:00 - 07:10: 均值=77.5秒, 区间=[67.8, 92.2]秒
  07:10 - 07:20: 均值=80.1秒, 区间=[70.8, 90.4]秒
  07:20 - 07:30: 均值=78.8秒, 区间=[68.0, 91.0]秒
  07:30 - 08:00: 均值=80.7秒, 区间=[72.0, 92.3]秒
  08:00 - 08:20: 均值=79.1秒, 区间=[71.6, 92.4]秒
  08:20 - 08:30: 均值=80.0秒, 区间=[71.7, 90.5]秒
  08:30 - 09:00: 均值=78.4秒, 区间=[73.0, 87.0]秒

路段 27-28:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 80.71
  07:00 - 07:10: 均值=471.1秒, 区间=[429.6, 506.8]秒
  07:10 - 07:30: 均值=475.5秒, 区间=[436.0, 523.0]秒
  07:30 - 07:40: 均值=484.3秒, 区间=[447.4, 536.4]秒
  07:40 - 07:50: 均值=493.4秒, 区间=[466.6, 518.1]秒
  07:50 - 08:10: 均值=485.8秒, 区间=[438.9, 520.6]秒
  08:10 - 08:20: 均值=479.9秒, 区间=[466.6, 491.4]秒
  08:20 - 09:00: 均值=470.5秒, 区间=[452.0, 527.5]秒

路段 27-35:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 226.74
  07:00 - 07:10: 均值=268.4秒, 区间=[232.8, 335.2]秒
  07:10 - 07:20: 均值=265.6秒, 区间=[235.1, 328.7]秒
  07:20 - 07:30: 均值=282.5秒, 区间=[237.8, 332.0]秒
  07:30 - 07:40: 均值=288.6秒, 区间=[261.0, 329.1]秒
  07:40 - 08:10: 均值=293.0秒, 区间=[260.9, 333.7]秒
  08:10 - 08:40: 均值=286.6秒, 区间=[252.2, 329.0]秒
  08:40 - 09:00: 均值=281.0秒, 区间=[249.8, 310.2]秒

路段 28-29:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 386.18
  07:00 - 07:20: 均值=216.3秒, 区间=[179.2, 251.0]秒
  07:20 - 07:30: 均值=221.2秒, 区间=[186.0, 277.0]秒
  07:30 - 07:40: 均值=224.0秒, 区间=[191.7, 261.7]秒
  07:40 - 07:50: 均值=217.9秒, 区间=[194.2, 249.2]秒
  07:50 - 08:00: 均值=233.0秒, 区间=[195.5, 265.6]秒
  08:00 - 08:10: 均值=234.2秒, 区间=[211.4, 264.4]秒
  08:10 - 09:00: 均值=228.4秒, 区间=[200.3, 263.0]秒

路段 28-36:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 41.69
  07:00 - 07:10: 均值=224.5秒, 区间=[190.4, 273.6]秒
  07:10 - 07:20: 均值=218.1秒, 区间=[175.8, 254.0]秒
  07:20 - 08:20: 均值=239.1秒, 区间=[201.0, 289.0]秒
  08:20 - 08:30: 均值=220.5秒, 区间=[184.0, 246.6]秒
  08:30 - 08:40: 均值=226.9秒, 区间=[199.3, 282.5]秒
  08:40 - 08:50: 均值=233.1秒, 区间=[201.9, 282.1]秒
  08:50 - 09:00: 均值=238.2秒, 区间=[202.8, 283.5]秒

路段 29-30:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 64.61
  07:00 - 07:10: 均值=83.3秒, 区间=[74.6, 96.3]秒
  07:10 - 07:20: 均值=82.1秒, 区间=[67.0, 97.1]秒
  07:20 - 07:30: 均值=84.2秒, 区间=[74.9, 104.2]秒
  07:30 - 07:40: 均值=86.4秒, 区间=[71.8, 105.0]秒
  07:40 - 08:20: 均值=90.1秒, 区间=[74.0, 106.0]秒
  08:20 - 08:40: 均值=89.0秒, 区间=[81.0, 101.0]秒
  08:40 - 09:00: 均值=87.4秒, 区间=[81.0, 100.0]秒

路段 29-37:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 23.72
  07:00 - 07:10: 均值=81.1秒, 区间=[53.5, 112.2]秒
  07:10 - 07:20: 均值=77.8秒, 区间=[48.9, 103.4]秒
  07:20 - 07:30: 均值=94.4秒, 区间=[67.0, 132.4]秒
  07:30 - 07:50: 均值=87.7秒, 区间=[65.0, 125.3]秒
  07:50 - 08:00: 均值=92.4秒, 区间=[65.9, 130.2]秒
  08:00 - 08:10: 均值=97.0秒, 区间=[70.8, 129.1]秒
  08:10 - 09:00: 均值=89.8秒, 区间=[61.2, 129.8]秒

路段 3-11:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 34.82
  07:00 - 07:20: 均值=187.2秒, 区间=[153.7, 229.1]秒
  07:20 - 07:40: 均值=192.4秒, 区间=[161.9, 220.8]秒
  07:40 - 08:10: 均值=201.5秒, 区间=[167.9, 243.1]秒
  08:10 - 08:30: 均值=196.9秒, 区间=[173.0, 232.1]秒
  08:30 - 08:40: 均值=200.8秒, 区间=[169.8, 246.4]秒
  08:40 - 08:50: 均值=192.4秒, 区间=[162.9, 240.3]秒
  08:50 - 09:00: 均值=200.5秒, 区间=[175.0, 239.4]秒

路段 3-4:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 229.36
  07:00 - 07:10: 均值=224.5秒, 区间=[196.9, 263.7]秒
  07:10 - 07:30: 均值=230.0秒, 区间=[202.9, 269.1]秒
  07:30 - 08:00: 均值=236.3秒, 区间=[197.8, 278.2]秒
  08:00 - 08:10: 均值=256.4秒, 区间=[226.7, 291.2]秒
  08:10 - 08:30: 均值=252.1秒, 区间=[225.4, 282.1]秒
  08:30 - 08:40: 均值=238.2秒, 区间=[210.6, 263.6]秒
  08:40 - 09:00: 均值=244.3秒, 区间=[215.9, 281.4]秒

路段 30-31:
  时段数量: 6 (目标: 8)
  划分方法: adaptive_threshold
  质量评分: 798.89
  07:00 - 07:20: 均值=135.0秒, 区间=[111.9, 176.2]秒
  07:20 - 07:30: 均值=138.7秒, 区间=[114.8, 178.2]秒
  07:30 - 07:40: 均值=142.2秒, 区间=[113.0, 181.1]秒
  07:40 - 07:50: 均值=149.0秒, 区间=[121.5, 178.8]秒
  07:50 - 08:00: 均值=156.0秒, 区间=[120.9, 199.0]秒
  08:00 - 09:00: 均值=170.0秒, 区间=[147.2, 191.8]秒

路段 30-38:
  时段数量: 8 (目标: 8)
  划分方法: change_rate
  质量评分: 1439.93
  07:00 - 07:10: 均值=115.5秒, 区间=[92.1, 141.2]秒
  07:10 - 07:30: 均值=119.4秒, 区间=[97.8, 152.9]秒
  07:30 - 07:40: 均值=124.1秒, 区间=[97.8, 160.0]秒
  07:40 - 08:00: 均值=128.0秒, 区间=[95.0, 167.5]秒
  08:00 - 08:10: 均值=145.4秒, 区间=[115.0, 174.2]秒
  08:10 - 08:20: 均值=148.9秒, 区间=[130.5, 174.8]秒
  08:20 - 08:30: 均值=146.3秒, 区间=[125.4, 176.1]秒
  08:30 - 09:00: 均值=144.7秒, 区间=[124.0, 175.3]秒

路段 31-32:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 429.72
  07:00 - 07:20: 均值=81.4秒, 区间=[65.0, 103.0]秒
  07:20 - 07:30: 均值=88.3秒, 区间=[69.5, 106.1]秒
  07:30 - 07:40: 均值=98.2秒, 区间=[81.0, 116.1]秒
  07:40 - 08:00: 均值=100.1秒, 区间=[75.0, 122.0]秒
  08:00 - 08:10: 均值=97.6秒, 区间=[75.8, 119.2]秒
  08:10 - 08:20: 均值=99.1秒, 区间=[75.5, 128.2]秒
  08:20 - 09:00: 均值=95.9秒, 区间=[77.0, 113.0]秒

路段 31-39:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 15.67
  07:00 - 07:10: 均值=57.3秒, 区间=[39.8, 90.0]秒
  07:10 - 07:20: 均值=54.8秒, 区间=[34.4, 81.6]秒
  07:20 - 07:50: 均值=56.8秒, 区间=[36.5, 85.0]秒
  07:50 - 08:00: 均值=58.6秒, 区间=[41.0, 81.6]秒
  08:00 - 08:10: 均值=55.9秒, 区间=[38.5, 82.2]秒
  08:10 - 08:50: 均值=58.6秒, 区间=[39.5, 85.1]秒
  08:50 - 09:00: 均值=53.9秒, 区间=[35.8, 82.9]秒

路段 32-40:
  时段数量: 8 (目标: 8)
  划分方法: change_rate
  质量评分: 113.12
  07:00 - 07:30: 均值=58.6秒, 区间=[45.7, 79.4]秒
  07:30 - 07:40: 均值=66.9秒, 区间=[49.9, 87.1]秒
  07:40 - 08:00: 均值=71.2秒, 区间=[49.6, 88.4]秒
  08:00 - 08:10: 均值=66.0秒, 区间=[53.0, 86.0]秒
  08:10 - 08:20: 均值=62.6秒, 区间=[50.8, 77.2]秒
  08:20 - 08:30: 均值=57.9秒, 区间=[50.0, 71.1]秒
  08:30 - 08:50: 均值=54.9秒, 区间=[46.0, 62.0]秒
  08:50 - 09:00: 均值=51.4秒, 区间=[47.0, 55.2]秒

路段 33-34:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 57.48
  07:00 - 07:10: 均值=197.4秒, 区间=[163.0, 224.3]秒
  07:10 - 07:30: 均值=204.6秒, 区间=[178.5, 229.0]秒
  07:30 - 07:40: 均值=195.8秒, 区间=[173.9, 225.6]秒
  07:40 - 08:00: 均值=204.0秒, 区间=[192.0, 221.7]秒
  08:00 - 08:30: 均值=197.0秒, 区间=[177.0, 232.0]秒
  08:30 - 08:50: 均值=187.5秒, 区间=[179.0, 199.1]秒
  08:50 - 09:00: 均值=192.5秒, 区间=[182.0, 220.0]秒

路段 34-35:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 342.93
  07:00 - 07:10: 均值=396.6秒, 区间=[350.8, 439.0]秒
  07:10 - 07:30: 均值=402.8秒, 区间=[369.0, 462.0]秒
  07:30 - 07:40: 均值=409.2秒, 区间=[359.0, 449.0]秒
  07:40 - 08:10: 均值=428.1秒, 区间=[383.7, 478.3]秒
  08:10 - 08:20: 均值=424.1秒, 区间=[391.8, 469.2]秒
  08:20 - 08:30: 均值=420.4秒, 区间=[387.8, 462.1]秒
  08:30 - 09:00: 均值=401.7秒, 区间=[370.7, 443.7]秒

路段 35-36:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 327.19
  07:00 - 07:10: 均值=106.4秒, 区间=[81.3, 160.3]秒
  07:10 - 07:30: 均值=114.9秒, 区间=[93.8, 147.4]秒
  07:30 - 07:40: 均值=121.4秒, 区间=[101.4, 149.1]秒
  07:40 - 08:20: 均值=130.2秒, 区间=[104.4, 167.6]秒
  08:20 - 08:30: 均值=120.6秒, 区间=[98.9, 145.9]秒
  08:30 - 08:40: 均值=128.3秒, 区间=[106.5, 158.5]秒
  08:40 - 09:00: 均值=132.6秒, 区间=[105.0, 162.5]秒

路段 36-37:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 113.88
  07:00 - 07:20: 均值=204.7秒, 区间=[159.0, 261.2]秒
  07:20 - 07:50: 均值=218.6秒, 区间=[186.1, 273.4]秒
  07:50 - 08:00: 均值=227.7秒, 区间=[195.6, 278.0]秒
  08:00 - 08:10: 均值=234.4秒, 区间=[197.5, 276.2]秒
  08:10 - 08:40: 均值=208.9秒, 区间=[183.0, 250.8]秒
  08:40 - 08:50: 均值=217.3秒, 区间=[184.4, 272.6]秒
  08:50 - 09:00: 均值=224.7秒, 区间=[187.9, 273.2]秒

路段 37-38:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 285.08
  07:00 - 07:10: 均值=196.3秒, 区间=[158.0, 263.4]秒
  07:10 - 07:20: 均值=200.0秒, 区间=[167.9, 251.1]秒
  07:20 - 07:40: 均值=210.2秒, 区间=[164.8, 274.2]秒
  07:40 - 08:00: 均值=213.9秒, 区间=[175.1, 250.0]秒
  08:00 - 08:10: 均值=249.7秒, 区间=[195.3, 287.2]秒
  08:10 - 08:20: 均值=245.5秒, 区间=[187.9, 297.3]秒
  08:20 - 09:00: 均值=213.6秒, 区间=[175.7, 262.4]秒

路段 38-39:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 71.52
  07:00 - 07:10: 均值=187.3秒, 区间=[162.0, 209.6]秒
  07:10 - 07:40: 均值=194.8秒, 区间=[161.8, 228.4]秒
  07:40 - 07:50: 均值=208.5秒, 区间=[174.7, 230.3]秒
  07:50 - 08:00: 均值=196.4秒, 区间=[162.3, 232.1]秒
  08:00 - 08:20: 均值=204.5秒, 区间=[165.6, 240.2]秒
  08:20 - 08:40: 均值=186.9秒, 区间=[158.8, 222.0]秒
  08:40 - 09:00: 均值=193.3秒, 区间=[152.9, 224.6]秒

路段 39-40:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 97.01
  07:00 - 07:10: 均值=166.4秒, 区间=[138.8, 209.8]秒
  07:10 - 07:20: 均值=170.9秒, 区间=[138.0, 217.4]秒
  07:20 - 07:30: 均值=178.3秒, 区间=[141.4, 202.2]秒
  07:30 - 07:40: 均值=190.8秒, 区间=[153.4, 224.4]秒
  07:40 - 08:00: 均值=202.3秒, 区间=[151.0, 239.0]秒
  08:00 - 08:30: 均值=192.4秒, 区间=[149.0, 234.2]秒
  08:30 - 09:00: 均值=179.2秒, 区间=[149.7, 213.3]秒

路段 4-12:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 53.29
  07:00 - 07:10: 均值=61.1秒, 区间=[52.0, 68.0]秒
  07:10 - 07:20: 均值=63.9秒, 区间=[58.0, 73.0]秒
  07:20 - 07:40: 均值=67.3秒, 区间=[53.8, 76.2]秒
  07:40 - 08:00: 均值=69.6秒, 区间=[64.8, 74.2]秒
  08:00 - 08:40: 均值=65.3秒, 区间=[53.7, 75.0]秒
  08:40 - 08:50: 均值=59.1秒, 区间=[51.0, 75.0]秒
  08:50 - 09:00: 均值=70.6秒, 区间=[59.9, 77.1]秒

路段 4-5:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 339.76
  07:00 - 07:10: 均值=71.8秒, 区间=[59.9, 94.0]秒
  07:10 - 07:20: 均值=77.2秒, 区间=[60.0, 97.4]秒
  07:20 - 07:30: 均值=73.0秒, 区间=[62.0, 94.6]秒
  07:30 - 07:40: 均值=74.1秒, 区间=[55.4, 105.2]秒
  07:40 - 08:30: 均值=78.6秒, 区间=[59.3, 103.4]秒
  08:30 - 08:50: 均值=76.8秒, 区间=[61.0, 95.0]秒
  08:50 - 09:00: 均值=85.3秒, 区间=[67.0, 106.0]秒

路段 5-13:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 53.70
  07:00 - 07:10: 均值=182.1秒, 区间=[146.5, 221.2]秒
  07:10 - 07:40: 均值=188.0秒, 区间=[146.0, 230.6]秒
  07:40 - 07:50: 均值=198.2秒, 区间=[151.9, 240.0]秒
  07:50 - 08:00: 均值=189.9秒, 区间=[145.8, 234.2]秒
  08:00 - 08:40: 均值=175.9秒, 区间=[138.8, 220.8]秒
  08:40 - 08:50: 均值=166.7秒, 区间=[130.9, 213.6]秒
  08:50 - 09:00: 均值=175.0秒, 区间=[141.3, 216.7]秒

路段 5-6:
  时段数量: 7 (目标: 7)
  划分方法: adaptive_threshold
  质量评分: 19.77
  07:00 - 07:10: 均值=61.0秒, 区间=[41.0, 92.1]秒
  07:10 - 07:20: 均值=63.9秒, 区间=[47.3, 88.4]秒
  07:20 - 07:30: 均值=61.5秒, 区间=[44.0, 86.1]秒
  07:30 - 07:40: 均值=66.3秒, 区间=[47.1, 89.3]秒
  07:40 - 08:00: 均值=68.9秒, 区间=[45.8, 95.0]秒
  08:00 - 08:10: 均值=64.5秒, 区间=[48.0, 88.2]秒
  08:10 - 09:00: 均值=62.0秒, 区间=[41.6, 91.0]秒

路段 6-14:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 43.32
  07:00 - 07:10: 均值=60.6秒, 区间=[48.0, 74.1]秒
  07:10 - 07:30: 均值=65.2秒, 区间=[52.0, 76.2]秒
  07:30 - 07:40: 均值=62.3秒, 区间=[51.6, 73.1]秒
  07:40 - 08:10: 均值=62.8秒, 区间=[50.8, 71.3]秒
  08:10 - 08:30: 均值=63.2秒, 区间=[49.0, 75.0]秒
  08:30 - 08:40: 均值=61.7秒, 区间=[52.7, 73.2]秒
  08:40 - 09:00: 均值=58.7秒, 区间=[50.9, 70.1]秒

路段 6-7:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 29.91
  07:00 - 07:10: 均值=67.9秒, 区间=[52.0, 92.1]秒
  07:10 - 07:20: 均值=64.6秒, 区间=[45.8, 91.0]秒
  07:20 - 07:50: 均值=71.3秒, 区间=[52.7, 97.3]秒
  07:50 - 08:00: 均值=68.3秒, 区间=[51.0, 91.3]秒
  08:00 - 08:20: 均值=76.9秒, 区间=[58.4, 98.6]秒
  08:20 - 08:50: 均值=70.1秒, 区间=[51.8, 86.0]秒
  08:50 - 09:00: 均值=73.5秒, 区间=[55.8, 93.8]秒

路段 7-15:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 24.42
  07:00 - 07:10: 均值=49.5秒, 区间=[34.5, 78.4]秒
  07:10 - 07:20: 均值=52.2秒, 区间=[31.9, 76.1]秒
  07:20 - 07:30: 均值=56.0秒, 区间=[37.9, 81.1]秒
  07:30 - 07:40: 均值=47.7秒, 区间=[21.8, 70.8]秒
  07:40 - 07:50: 均值=50.7秒, 区间=[34.9, 69.2]秒
  07:50 - 08:20: 均值=56.8秒, 区间=[36.7, 80.3]秒
  08:20 - 09:00: 均值=50.6秒, 区间=[32.0, 74.0]秒

路段 7-8:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 32.40
  07:00 - 07:10: 均值=253.0秒, 区间=[215.6, 293.6]秒
  07:10 - 07:20: 均值=262.4秒, 区间=[222.6, 296.2]秒
  07:20 - 07:30: 均值=251.7秒, 区间=[217.6, 284.1]秒
  07:30 - 07:40: 均值=259.6秒, 区间=[222.8, 309.8]秒
  07:40 - 07:50: 均值=265.9秒, 区间=[224.7, 298.0]秒
  07:50 - 08:20: 均值=248.8秒, 区间=[209.2, 293.5]秒
  08:20 - 09:00: 均值=252.4秒, 区间=[216.8, 296.2]秒

路段 8-16:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 44.70
  07:00 - 07:30: 均值=269.4秒, 区间=[234.0, 309.0]秒
  07:30 - 08:00: 均值=278.6秒, 区间=[249.0, 314.2]秒
  08:00 - 08:10: 均值=276.4秒, 区间=[251.7, 296.1]秒
  08:10 - 08:20: 均值=283.1秒, 区间=[257.0, 316.7]秒
  08:20 - 08:40: 均值=279.2秒, 区间=[247.0, 311.9]秒
  08:40 - 08:50: 均值=273.8秒, 区间=[255.8, 292.6]秒
  08:50 - 09:00: 均值=279.1秒, 区间=[258.6, 310.6]秒

路段 9-10:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 64.60
  07:00 - 07:20: 均值=101.7秒, 区间=[84.7, 117.2]秒
  07:20 - 07:40: 均值=109.7秒, 区间=[87.1, 132.0]秒
  07:40 - 07:50: 均值=112.7秒, 区间=[95.6, 130.1]秒
  07:50 - 08:00: 均值=116.2秒, 区间=[91.0, 135.5]秒
  08:00 - 08:10: 均值=111.5秒, 区间=[90.9, 132.4]秒
  08:10 - 08:40: 均值=105.9秒, 区间=[90.9, 134.1]秒
  08:40 - 09:00: 均值=105.0秒, 区间=[94.0, 127.0]秒

路段 9-17:
  时段数量: 7 (目标: 7)
  划分方法: change_rate
  质量评分: 40.47
  07:00 - 07:10: 均值=381.9秒, 区间=[317.9, 409.4]秒
  07:10 - 07:20: 均值=372.9秒, 区间=[314.0, 436.2]秒
  07:20 - 07:40: 均值=382.4秒, 区间=[320.3, 449.7]秒
  07:40 - 08:00: 均值=404.9秒, 区间=[340.8, 462.2]秒
  08:00 - 08:40: 均值=394.6秒, 区间=[357.1, 452.2]秒
  08:40 - 08:50: 均值=382.3秒, 区间=[351.8, 410.4]秒
  08:50 - 09:00: 均值=369.2秒, 区间=[349.9, 405.2]秒
