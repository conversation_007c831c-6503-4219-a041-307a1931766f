#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件处理脚本
删除指定列并保留需要的列：路段ID、采集时间、通行时间(秒)、距离(米)
"""

import pandas as pd
import os
import glob

def process_csv_file(file_path):
    """
    处理单个CSV文件，删除指定列
    
    Args:
        file_path (str): CSV文件路径
    """
    try:
        print(f"正在处理文件: {file_path}")
        
        # 读取CSV文件，使用UTF-8编码
        df = pd.read_csv(file_path, encoding='utf-8')
        
        print(f"原始文件列数: {len(df.columns)}")
        print(f"原始文件行数: {len(df)}")
        print(f"原始列名: {list(df.columns)}")
        
        # 定义需要保留的列
        columns_to_keep = ['路段ID', '采集时间', '通行时间(秒)', '距离(米)']
        
        # 检查所有需要保留的列是否存在
        missing_columns = [col for col in columns_to_keep if col not in df.columns]
        if missing_columns:
            print(f"警告: 文件 {file_path} 中缺少以下列: {missing_columns}")
            return False
        
        # 只保留需要的列
        df_filtered = df[columns_to_keep].copy()
        
        print(f"处理后列数: {len(df_filtered.columns)}")
        print(f"处理后行数: {len(df_filtered)}")
        print(f"保留的列名: {list(df_filtered.columns)}")
        
        # 保存处理后的文件（覆盖原文件）
        df_filtered.to_csv(file_path, index=False, encoding='utf-8')
        
        print(f"文件 {file_path} 处理完成！")
        print("-" * 50)
        return True
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return False

def main():
    """
    主函数：批量处理采集数据目录中的所有CSV文件
    """
    # 设置数据目录
    data_dir = "采集数据/早高峰"
    
    # 检查目录是否存在
    if not os.path.exists(data_dir):
        print(f"错误: 目录 {data_dir} 不存在！")
        return
    
    # 获取所有CSV文件
    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
    
    if not csv_files:
        print(f"在目录 {data_dir} 中没有找到CSV文件！")
        return
    
    print(f"找到 {len(csv_files)} 个CSV文件:")
    for file in csv_files:
        print(f"  - {file}")
    print("=" * 60)
    
    # 处理每个CSV文件
    success_count = 0
    for csv_file in csv_files:
        if process_csv_file(csv_file):
            success_count += 1
    
    print("=" * 60)
    print(f"处理完成！成功处理 {success_count}/{len(csv_files)} 个文件")
    
    # 验证处理结果
    print("\n验证处理结果:")
    for csv_file in csv_files:
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            print(f"{os.path.basename(csv_file)}: {len(df.columns)} 列, {len(df)} 行")
            print(f"  列名: {list(df.columns)}")
        except Exception as e:
            print(f"验证文件 {csv_file} 时出错: {str(e)}")

if __name__ == "__main__":
    main()
